#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Assistente de Texto com IA
Aplicação Windows para assistência de texto usando OpenAI ChatGPT
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import threading
import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_text_assistant.log'),
        logging.StreamHandler()
    ]
)

def main():
    """Função principal da aplicação"""
    try:
        # Verificar se já existe uma instância rodando
        import psutil
        current_pid = os.getpid()
        for proc in psutil.process_iter(['pid', 'name']):
            if proc.info['name'] == 'ai_text_assistant.exe' and proc.info['pid'] != current_pid:
                messagebox.showwarning(
                    "Assistente de Texto IA", 
                    "O aplicativo já está em execução na bandeja do sistema."
                )
                sys.exit(0)
    except ImportError:
        pass  # psutil não disponível, continuar normalmente
    
    # Importar componentes principais
    from src.tray_app import TrayApp
    from src.config_manager import ConfigManager
    
    # Inicializar configurações
    config_manager = ConfigManager()
    
    # Verificar primeira execução
    if not config_manager.is_configured():
        from src.setup_wizard import SetupWizard
        setup = SetupWizard()
        if not setup.run():
            sys.exit(0)
    
    # Inicializar aplicação principal
    app = TrayApp(config_manager)
    
    try:
        app.run()
    except KeyboardInterrupt:
        logging.info("Aplicação interrompida pelo usuário")
    except Exception as e:
        logging.error(f"Erro na aplicação: {e}")
        messagebox.showerror("Erro", f"Erro inesperado: {e}")
    finally:
        app.cleanup()

if __name__ == "__main__":
    main()
