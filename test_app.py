#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de Teste do Assistente de Texto IA
Testa componentes principais da aplicação
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

class TestConfigManager(unittest.TestCase):
    """Testes para o gerenciador de configurações"""
    
    def setUp(self):
        from config_manager import ConfigManager
        self.config = ConfigManager('test_config.ini')
    
    def test_encryption_decryption(self):
        """Testa criptografia e descriptografia"""
        test_data = "sk-test123456789"
        encrypted = self.config.encrypt_data(test_data)
        decrypted = self.config.decrypt_data(encrypted)
        self.assertEqual(test_data, decrypted)
    
    def test_api_key_storage(self):
        """Testa armazenamento seguro da API key"""
        test_key = "sk-test123456789"
        self.config.set_api_key(test_key)
        retrieved_key = self.config.get_api_key()
        self.assertEqual(test_key, retrieved_key)
    
    def test_custom_prompts(self):
        """Testa prompts personalizados"""
        self.config.add_custom_prompt("Teste", "Este é um prompt de teste:")
        prompts = self.config.get_custom_prompts()
        self.assertIn("Teste", prompts)
        self.assertEqual(prompts["Teste"], "Este é um prompt de teste:")
    
    def tearDown(self):
        # Limpar arquivo de teste
        if os.path.exists('test_config.ini'):
            os.remove('test_config.ini')

class TestAIClient(unittest.TestCase):
    """Testes para o cliente da OpenAI"""
    
    def setUp(self):
        from config_manager import ConfigManager
        from ai_client import AIClient
        
        self.config = ConfigManager('test_config.ini')
        self.config.set_api_key("sk-test123456789")
        self.ai_client = AIClient(self.config)
    
    def test_available_actions(self):
        """Testa obtenção de ações disponíveis"""
        actions = self.ai_client.get_available_actions()
        self.assertIsInstance(actions, dict)
        self.assertIn('grammar_correction', actions)
    
    def test_text_validation(self):
        """Testa validação de comprimento de texto"""
        short_text = "Texto curto"
        long_text = "a" * 10000
        
        self.assertTrue(self.ai_client.validate_text_length(short_text))
        self.assertFalse(self.ai_client.validate_text_length(long_text))
    
    def test_token_estimation(self):
        """Testa estimativa de tokens"""
        text = "Este é um texto de teste"
        tokens = self.ai_client.estimate_tokens(text)
        self.assertGreater(tokens, 0)
        self.assertLess(tokens, 100)
    
    def tearDown(self):
        if os.path.exists('test_config.ini'):
            os.remove('test_config.ini')

class TestTextMonitor(unittest.TestCase):
    """Testes para o monitor de texto"""
    
    def setUp(self):
        from config_manager import ConfigManager
        from text_monitor import TextMonitor
        
        self.config = ConfigManager('test_config.ini')
        self.callback_called = False
        self.callback_text = None
        
        def test_callback(text):
            self.callback_called = True
            self.callback_text = text
        
        self.monitor = TextMonitor(self.config, test_callback)
    
    def test_text_validation(self):
        """Testa validação de seleção de texto"""
        valid_text = "Este é um texto válido para processamento"
        invalid_short = "ab"
        invalid_url = "https://www.example.com"
        invalid_email = "<EMAIL>"
        
        self.assertTrue(self.monitor._is_valid_text_selection(valid_text))
        self.assertFalse(self.monitor._is_valid_text_selection(invalid_short))
        self.assertFalse(self.monitor._is_valid_text_selection(invalid_url))
        self.assertFalse(self.monitor._is_valid_text_selection(invalid_email))
    
    def test_shortcut_parsing(self):
        """Testa parsing de atalhos de teclado"""
        shortcut = "ctrl+shift+a"
        parsed = self.monitor._parse_shortcut(shortcut)
        self.assertIsInstance(parsed, set)
        self.assertEqual(len(parsed), 3)  # ctrl, shift, a
    
    def tearDown(self):
        if os.path.exists('test_config.ini'):
            os.remove('test_config.ini')

def run_basic_tests():
    """Executa testes básicos sem dependências externas"""
    print("========================================")
    print("  Assistente de Texto IA - Testes")
    print("========================================")
    print()
    
    # Teste 1: Importação de módulos
    print("[1/5] Testando importação de módulos...")
    try:
        from src.config_manager import ConfigManager
        from src.ai_client import AIClient
        from src.text_monitor import TextMonitor
        from src.tray_app import TrayApp
        print("✓ Todos os módulos importados com sucesso")
    except ImportError as e:
        print(f"✗ Erro na importação: {e}")
        return False
    
    # Teste 2: Configuração básica
    print("\n[2/5] Testando configuração básica...")
    try:
        config = ConfigManager('test_config.ini')
        config.set('Test', 'key', 'value')
        value = config.get('Test', 'key')
        assert value == 'value'
        print("✓ Configuração funcionando")
        
        # Limpar
        if os.path.exists('test_config.ini'):
            os.remove('test_config.ini')
    except Exception as e:
        print(f"✗ Erro na configuração: {e}")
        return False
    
    # Teste 3: Criptografia
    print("\n[3/5] Testando criptografia...")
    try:
        config = ConfigManager('test_config.ini')
        test_data = "dados_secretos_123"
        encrypted = config.encrypt_data(test_data)
        decrypted = config.decrypt_data(encrypted)
        assert decrypted == test_data
        print("✓ Criptografia funcionando")
        
        # Limpar
        if os.path.exists('test_config.ini'):
            os.remove('test_config.ini')
    except Exception as e:
        print(f"✗ Erro na criptografia: {e}")
        return False
    
    # Teste 4: Cliente AI (sem conexão real)
    print("\n[4/5] Testando cliente AI...")
    try:
        config = ConfigManager('test_config.ini')
        config.set_api_key("sk-test123456789")
        ai_client = AIClient(config)
        actions = ai_client.get_available_actions()
        assert isinstance(actions, dict)
        assert len(actions) > 0
        print("✓ Cliente AI inicializado")
        
        # Limpar
        if os.path.exists('test_config.ini'):
            os.remove('test_config.ini')
    except Exception as e:
        print(f"✗ Erro no cliente AI: {e}")
        return False
    
    # Teste 5: Validação de texto
    print("\n[5/5] Testando validação de texto...")
    try:
        config = ConfigManager('test_config.ini')
        
        def dummy_callback(text):
            pass
        
        monitor = TextMonitor(config, dummy_callback)
        
        # Testes de validação
        assert monitor._is_valid_text_selection("Texto válido para teste")
        assert not monitor._is_valid_text_selection("ab")  # Muito curto
        assert not monitor._is_valid_text_selection("https://example.com")  # URL
        
        print("✓ Validação de texto funcionando")
        
        # Limpar
        if os.path.exists('test_config.ini'):
            os.remove('test_config.ini')
    except Exception as e:
        print(f"✗ Erro na validação: {e}")
        return False
    
    print("\n========================================")
    print("  ✓ Todos os testes básicos passaram!")
    print("========================================")
    print()
    print("Para testar a aplicação completa:")
    print("1. Configure uma API key válida da OpenAI")
    print("2. Execute: python main.py")
    print("3. Siga o assistente de configuração")
    print()
    
    return True

def run_unit_tests():
    """Executa testes unitários completos"""
    print("Executando testes unitários...")
    
    # Descobrir e executar testes
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromModule(sys.modules[__name__])
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--unit":
        success = run_unit_tests()
    else:
        success = run_basic_tests()
    
    sys.exit(0 if success else 1)
