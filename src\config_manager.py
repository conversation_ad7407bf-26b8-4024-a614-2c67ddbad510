#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gerenciador de Configurações
Responsável por carregar, salvar e criptografar configurações da aplicação
"""

import os
import configparser
import json
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import logging

class ConfigManager:
    def __init__(self, config_file='config.ini'):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.encryption_key = None
        self.load_config()
    
    def _generate_key(self, password: str) -> bytes:
        """Gera chave de criptografia baseada em senha"""
        password_bytes = password.encode()
        salt = b'ai_text_assistant_salt'  # Em produção, usar salt aleatório
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
        return key
    
    def _get_machine_id(self) -> str:
        """Obtém ID único da máquina para criptografia"""
        try:
            import uuid
            return str(uuid.getnode())
        except:
            return "default_machine_id"
    
    def load_config(self):
        """Carrega configurações do arquivo"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file, encoding='utf-8')
            else:
                self._create_default_config()
            
            # Inicializar chave de criptografia
            machine_id = self._get_machine_id()
            self.encryption_key = self._generate_key(machine_id)
            
        except Exception as e:
            logging.error(f"Erro ao carregar configurações: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """Cria arquivo de configuração padrão"""
        self.config['OpenAI'] = {
            'api_key': '',
            'model': 'gpt-3.5-turbo',
            'max_tokens': '500',
            'temperature': '0.7'
        }
        
        self.config['Interface'] = {
            'theme': 'dark',
            'language': 'pt-br',
            'show_notifications': 'true',
            'auto_start': 'false'
        }
        
        self.config['Shortcuts'] = {
            'activate_menu': 'ctrl+shift+a',
            'quick_correct': 'ctrl+shift+c',
            'quick_translate': 'ctrl+shift+t',
            'quick_summarize': 'ctrl+shift+s'
        }
        
        self.config['Actions'] = {
            'grammar_correction': 'true',
            'text_summarization': 'true',
            'translation': 'true',
            'tone_adjustment': 'true',
            'text_expansion': 'true',
            'custom_prompts': 'true'
        }
        
        self.config['CustomPrompts'] = {
            'prompt1_name': 'Melhorar Escrita',
            'prompt1_text': 'Melhore a escrita do seguinte texto, mantendo o significado original:',
            'prompt2_name': 'Explicar Conceito',
            'prompt2_text': 'Explique o seguinte conceito de forma simples e clara:',
            'prompt3_name': 'Criar Lista',
            'prompt3_text': 'Transforme o seguinte texto em uma lista organizada:'
        }
        
        self.save_config()
    
    def save_config(self):
        """Salva configurações no arquivo"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
        except Exception as e:
            logging.error(f"Erro ao salvar configurações: {e}")
    
    def encrypt_data(self, data: str) -> str:
        """Criptografa dados sensíveis"""
        try:
            if not self.encryption_key:
                return data
            
            fernet = Fernet(self.encryption_key)
            encrypted_data = fernet.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logging.error(f"Erro ao criptografar dados: {e}")
            return data
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Descriptografa dados sensíveis"""
        try:
            if not self.encryption_key or not encrypted_data:
                return encrypted_data
            
            fernet = Fernet(self.encryption_key)
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = fernet.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            logging.error(f"Erro ao descriptografar dados: {e}")
            return encrypted_data
    
    def get_api_key(self) -> str:
        """Obtém API key descriptografada"""
        encrypted_key = self.config.get('OpenAI', 'api_key', fallback='')
        if encrypted_key:
            return self.decrypt_data(encrypted_key)
        return ''
    
    def set_api_key(self, api_key: str):
        """Define API key criptografada"""
        encrypted_key = self.encrypt_data(api_key)
        self.config.set('OpenAI', 'api_key', encrypted_key)
        self.save_config()
    
    def get(self, section: str, key: str, fallback=None):
        """Obtém valor de configuração"""
        return self.config.get(section, key, fallback=fallback)
    
    def set(self, section: str, key: str, value: str):
        """Define valor de configuração"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, key, value)
        self.save_config()
    
    def get_boolean(self, section: str, key: str, fallback=False):
        """Obtém valor booleano de configuração"""
        return self.config.getboolean(section, key, fallback=fallback)
    
    def is_configured(self) -> bool:
        """Verifica se a aplicação está configurada"""
        api_key = self.get_api_key()
        return bool(api_key and api_key.strip())
    
    def get_custom_prompts(self) -> dict:
        """Obtém prompts personalizados"""
        prompts = {}
        if self.config.has_section('CustomPrompts'):
            items = self.config.items('CustomPrompts')
            for key, value in items:
                if key.endswith('_name'):
                    prompt_id = key.replace('_name', '')
                    text_key = f"{prompt_id}_text"
                    if self.config.has_option('CustomPrompts', text_key):
                        prompts[value] = self.config.get('CustomPrompts', text_key)
        return prompts
    
    def add_custom_prompt(self, name: str, text: str):
        """Adiciona prompt personalizado"""
        # Encontrar próximo ID disponível
        existing_ids = []
        if self.config.has_section('CustomPrompts'):
            for key in self.config.options('CustomPrompts'):
                if key.endswith('_name'):
                    prompt_id = key.replace('_name', '')
                    if prompt_id.startswith('prompt'):
                        try:
                            existing_ids.append(int(prompt_id.replace('prompt', '')))
                        except ValueError:
                            pass
        
        next_id = max(existing_ids, default=0) + 1
        prompt_id = f"prompt{next_id}"
        
        self.set('CustomPrompts', f"{prompt_id}_name", name)
        self.set('CustomPrompts', f"{prompt_id}_text", text)

    def remove_custom_prompt(self, name: str):
        """Remove prompt personalizado"""
        if not self.config.has_section('CustomPrompts'):
            return

        # Encontrar prompt pelo nome
        prompt_id = None
        for key in self.config.options('CustomPrompts'):
            if key.endswith('_name') and self.config.get('CustomPrompts', key) == name:
                prompt_id = key.replace('_name', '')
                break

        if prompt_id:
            # Remover nome e texto do prompt
            name_key = f"{prompt_id}_name"
            text_key = f"{prompt_id}_text"

            if self.config.has_option('CustomPrompts', name_key):
                self.config.remove_option('CustomPrompts', name_key)
            if self.config.has_option('CustomPrompts', text_key):
                self.config.remove_option('CustomPrompts', text_key)

            self.save_config()
