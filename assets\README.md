# Assets

Esta pasta contém recursos visuais da aplicação.

## Arquivos Recomendados

### icon.png
- Ícone principal da aplicação (64x64 pixels)
- Formato PNG com transparência
- Usado na bandeja do sistema

### icon.ico
- Ícone no formato ICO para o executável
- Múltiplas resoluções (16x16, 32x32, 48x48, 64x64)
- Usado pelo cx_Freeze no executável final

## Como Adicionar Ícones

1. Crie ou obtenha um ícone de 64x64 pixels
2. Salve como `icon.png` nesta pasta
3. Converta para `icon.ico` usando uma ferramenta online ou software de edição
4. Execute o build novamente

## Ferramentas Recomendadas

- **Online**: favicon.io, convertio.co
- **Software**: GIMP, Paint.NET, Photoshop
- **Comando**: ImageMagick (`convert icon.png icon.ico`)

Se não houver ícones personalizados, a aplicação criará um ícone simples programaticamente.
