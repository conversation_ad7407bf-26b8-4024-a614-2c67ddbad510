#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Setup script para cx_Freeze
Gera executável Windows standalone do Assistente de Texto IA
"""

import sys
import os
from cx_Freeze import setup, Executable

# Configurações da aplicação
APP_NAME = "Assistente de Texto IA"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "Assistente de texto com inteligência artificial para Windows"
APP_AUTHOR = "Desenvolvedor"

# Arquivos e pastas a incluir
include_files = [
    "config.ini",
    "README.md",
    ("assets", "assets") if os.path.exists("assets") else None
]

# Remover None da lista
include_files = [f for f in include_files if f is not None]

# Pacotes a incluir
packages = [
    "tkinter",
    "tkinter.ttk",
    "tkinter.messagebox",
    "tkinter.scrolledtext",
    "sv_ttk",
    "pystray",
    "PIL",
    "pynput",
    "win32clipboard",
    "win32con",
    "win32api",
    "win32gui",
    "openai",
    "requests",
    "cryptography",
    "configparser",
    "threading",
    "logging",
    "json",
    "base64",
    "hashlib",
    "uuid",
    "psutil",
    "webbrowser",
    "subprocess",
    "time"
]

# Módulos a incluir explicitamente
includes = [
    "src.tray_app",
    "src.config_manager",
    "src.text_monitor",
    "src.ai_client",
    "src.settings_window",
    "src.context_menu",
    "src.response_window",
    "src.setup_wizard",
    "src.prompt_editor",
    "src.quick_actions",
    "src.loading_window"
]

# Módulos a excluir
excludes = [
    "test",
    "tests",
    "unittest",
    "email",
    "html",
    "http",
    "urllib",
    "xml",
    "pydoc",
    "doctest",
    "argparse",
    "difflib",
    "inspect",
    "calendar",
    "collections.abc"
]

# Opções de build
build_exe_options = {
    "packages": packages,
    "includes": includes,
    "excludes": excludes,
    "include_files": include_files,
    "optimize": 2,
    "include_msvcrt": True,
    "silent": True,
    "zip_include_packages": ["*"],
    "zip_exclude_packages": [],
    "build_exe": "build/exe"
}

# Configuração do executável principal
exe_options = {
    "script": "main.py",
    "base": "Win32GUI",  # Para aplicação Windows sem console
    "target_name": "AssistenteTextoIA.exe",
    "icon": "assets/icon.ico" if os.path.exists("assets/icon.ico") else None,
    "copyright": f"© 2024 {APP_AUTHOR}",
    "trademarks": APP_NAME
}

# Criar executável
executables = [Executable(**exe_options)]

# Configuração do MSI (instalador Windows)
bdist_msi_options = {
    "upgrade_code": "{12345678-1234-1234-1234-123456789012}",
    "add_to_path": False,
    "initial_target_dir": f"[ProgramFilesFolder]\\{APP_NAME}",
    "install_icon": "assets/icon.ico" if os.path.exists("assets/icon.ico") else None,
    "summary_data": {
        "author": APP_AUTHOR,
        "comments": APP_DESCRIPTION,
        "keywords": "IA, Texto, Assistente, OpenAI, ChatGPT"
    }
}

# Setup principal
setup(
    name=APP_NAME,
    version=APP_VERSION,
    description=APP_DESCRIPTION,
    author=APP_AUTHOR,
    executables=executables,
    options={
        "build_exe": build_exe_options,
        "bdist_msi": bdist_msi_options
    },
    requires=[
        "tkinter",
        "sv-ttk",
        "pystray",
        "Pillow",
        "pynput",
        "pywin32",
        "openai",
        "requests",
        "cryptography",
        "psutil"
    ]
)
