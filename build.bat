@echo off
echo ========================================
echo  Assistente de Texto IA - Build Script
echo ========================================
echo.

REM Verificar se Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python não encontrado. Instale Python 3.9+ primeiro.
    pause
    exit /b 1
)

echo [1/5] Verificando dependências...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERRO: Falha ao instalar dependências.
    pause
    exit /b 1
)

echo.
echo [2/5] Lim<PERSON>do builds anteriores...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist

echo.
echo [3/5] Criando pasta de assets...
if not exist assets mkdir assets

echo.
echo [4/5] Gerando executável...
python setup.py build
if errorlevel 1 (
    echo ERRO: Falha ao gerar executável.
    pause
    exit /b 1
)

echo.
echo [5/5] Criando instalador MSI (opcional)...
python setup.py bdist_msi
if errorlevel 1 (
    echo AVISO: Falha ao criar instalador MSI (opcional).
)

echo.
echo ========================================
echo  Build concluído com sucesso!
echo ========================================
echo.
echo Executável gerado em: build\exe\AssistenteTextoIA.exe
if exist dist\*.msi echo Instalador MSI em: dist\
echo.
echo Para testar, execute:
echo   build\exe\AssistenteTextoIA.exe
echo.
pause
