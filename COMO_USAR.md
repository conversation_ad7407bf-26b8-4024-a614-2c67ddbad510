# Como Usar o Assistente de Texto IA

## 🚀 Início Rápido

### 1. Primeira Execução
```bash
# Opção 1: Executar diretamente
python main.py

# Opção 2: Usar instalador
install.bat

# Opção 3: Compilar executável
build.bat
```

### 2. Configuração Inicial
1. **API Key da OpenAI**
   - Acesse: https://platform.openai.com/api-keys
   - Crie uma nova API key
   - Cole no assistente de configuração

2. **Escolher Modelo**
   - `gpt-3.5-turbo` (recomendado, mais rápido e barato)
   - `gpt-4` (mais preciso, mais caro)

3. **Configurar Preferências**
   - Notificações do sistema
   - Inicialização automática
   - Atalhos de teclado

## 📝 Usando o Assistente

### Método 1: Menu Completo
1. **Selecione texto** em qualquer aplicação (Word, navegador, Notepad, etc.)
2. **Pressione `Ctrl+Shift+A`**
3. **Escolha uma ação** no menu que aparece
4. **Aguarde o processamento**
5. **Visualize o resultado** e escolha:
   - Copiar para área de transferência
   - Substituir texto original
   - Editar antes de usar

### Método 2: Atalhos Rápidos
- **`Ctrl+Shift+C`** - Correção gramatical automática
- **`Ctrl+Shift+T`** - Tradução para inglês
- **`Ctrl+Shift+S`** - Resumo automático

### Método 3: Prompts Personalizados
1. **Configure prompts** nas configurações
2. **Selecione texto** e use `Ctrl+Shift+A`
3. **Escolha seu prompt** personalizado

## 🎯 Exemplos Práticos

### Correção de Texto
**Antes:**
```
Eu gosto muito de programar em python e criar aplicacoes uteis para resolver problemas do dia a dia
```

**Depois:**
```
Eu gosto muito de programar em Python e criar aplicações úteis para resolver problemas do dia a dia.
```

### Resumo de Artigo
**Antes:** [Artigo longo de 500 palavras]

**Depois:**
```
Resumo: O artigo discute os principais benefícios da inteligência artificial na automação de tarefas, destacando três pontos principais: eficiência, precisão e economia de tempo.
```

### Tradução
**Antes:**
```
Como você está hoje? Espero que esteja tudo bem!
```

**Depois:**
```
How are you today? I hope everything is fine!
```

### Ajuste de Tom
**Entrada Casual:**
```
Oi, precisa fazer isso rapidinho pra mim
```

**Saída Formal:**
```
Prezado(a), solicito gentilmente que realize esta tarefa com brevidade, se possível.
```

## ⚙️ Configurações Avançadas

### Acessar Configurações
- **Clique direito** no ícone da bandeja
- **Selecione "Configurações"**

### Seções Disponíveis

#### 1. OpenAI
- **API Key**: Sua chave de acesso
- **Modelo**: gpt-3.5-turbo, gpt-4, etc.
- **Max Tokens**: Limite de resposta (100-4000)
- **Temperature**: Criatividade (0.0-2.0)

#### 2. Interface
- **Tema**: Claro ou escuro
- **Notificações**: Ativar/desativar
- **Auto Iniciar**: Iniciar com Windows

#### 3. Ações
Ativar/desativar funcionalidades:
- ✅ Correção gramatical
- ✅ Resumo de texto
- ✅ Tradução
- ✅ Ajuste de tom
- ✅ Expansão de texto
- ✅ Prompts personalizados

#### 4. Atalhos
Personalizar combinações de teclas:
- Menu principal: `ctrl+shift+a`
- Correção rápida: `ctrl+shift+c`
- Tradução rápida: `ctrl+shift+t`
- Resumo rápido: `ctrl+shift+s`

#### 5. Prompts Personalizados
Criar seus próprios comandos:
```
Nome: Melhorar Apresentação
Prompt: Transforme o seguinte texto em uma apresentação profissional com tópicos organizados:
```

## 🔧 Solução de Problemas

### Problema: "API key inválida"
**Soluções:**
1. Verifique se a API key está correta
2. Confirme se há créditos na conta OpenAI
3. Teste a conexão nas configurações

### Problema: "Texto não detectado"
**Soluções:**
1. Certifique-se que o texto está selecionado
2. Verifique se o monitoramento está ativo
3. Tente usar `Ctrl+C` antes do atalho

### Problema: "Aplicação não responde"
**Soluções:**
1. Verifique o arquivo de log: `ai_text_assistant.log`
2. Reinicie a aplicação
3. Execute como administrador

### Problema: "Atalhos não funcionam"
**Soluções:**
1. Verifique se outra aplicação usa os mesmos atalhos
2. Personalize os atalhos nas configurações
3. Execute a aplicação como administrador

## 📊 Dicas de Uso

### Para Melhores Resultados
1. **Selecione texto completo** (frases inteiras)
2. **Use contexto suficiente** (pelo menos 10-20 palavras)
3. **Seja específico** com prompts personalizados
4. **Revise sempre** as respostas da IA

### Economia de Tokens
1. **Use gpt-3.5-turbo** para tarefas simples
2. **Limite o texto selecionado** (máximo 1000 palavras)
3. **Configure max_tokens** adequadamente
4. **Use ações específicas** em vez de prompts genéricos

### Produtividade
1. **Configure atalhos** para ações frequentes
2. **Crie prompts personalizados** para suas necessidades
3. **Use auto-substituição** para correções rápidas
4. **Ative notificações** para feedback visual

## 🔒 Privacidade e Segurança

### Dados Locais
- **API key criptografada** localmente
- **Configurações seguras** no computador
- **Sem telemetria** ou coleta de dados

### Dados Enviados
- **Apenas texto selecionado** é enviado para OpenAI
- **Nenhum dado pessoal** adicional
- **Comunicação criptografada** (HTTPS)

### Recomendações
- **Não processe dados sensíveis** (senhas, documentos confidenciais)
- **Revise textos** antes de enviar
- **Use conta OpenAI dedicada** se necessário

## 📞 Suporte

### Logs e Depuração
- **Arquivo de log**: `ai_text_assistant.log`
- **Nível de log**: INFO, WARNING, ERROR
- **Rotação automática** de logs

### Contato
- **Issues**: GitHub Issues
- **Documentação**: README.md
- **Código fonte**: Disponível no repositório

---

**Desenvolvido com ❤️ para facilitar seu trabalho com texto!**
