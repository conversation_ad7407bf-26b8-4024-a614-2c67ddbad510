@echo off
echo ========================================
echo  Instalando Dependencias - Assistente IA
echo ========================================
echo.

echo [1/8] Atualizando pip...
python -m pip install --upgrade pip

echo.
echo [2/8] Instalando sv-ttk (tema)...
pip install sv-ttk

echo.
echo [3/8] Instalando pystray (bandeja)...
pip install pystray

echo.
echo [4/8] Instalando Pillow (imagens)...
pip install Pillow

echo.
echo [5/8] Instalando pynput (teclado)...
pip install pynput

echo.
echo [6/8] Instalando pywin32 (Windows API)...
pip install pywin32

echo.
echo [7/8] Instalando OpenAI...
pip install openai requests

echo.
echo [8/8] Instalando utilitarios...
pip install cryptography psutil cx-Freeze

echo.
echo ========================================
echo  Instalacao concluida!
echo ========================================
echo.
echo Para testar, execute: python main.py
pause
