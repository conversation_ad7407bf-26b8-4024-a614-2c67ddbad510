#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cliente OpenAI
Gerencia comunicação com a API do ChatGPT
"""

import openai
import logging
import time
from typing import Optional, Dict, Any
from .config_manager import ConfigManager

class AIClient:
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.client = None
        self._init_client()
        
        # Prompts predefinidos em português
        self.predefined_prompts = {
            'grammar_correction': {
                'name': 'Correção Gramatical',
                'prompt': 'Corrija a gramática e ortografia do seguinte texto em português, mantendo o significado original. Retorne apenas o texto corrigido:'
            },
            'text_summarization': {
                'name': 'Resumo',
                'prompt': 'Faça um resumo conciso e claro do seguinte texto em português:'
            },
            'translation_en': {
                'name': 'Tradução para Inglês',
                'prompt': 'Traduza o seguinte texto para o inglês de forma natural e fluente:'
            },
            'translation_es': {
                'name': 'Tradução para Espanhol',
                'prompt': 'Traduza o seguinte texto para o espanhol de forma natural e fluente:'
            },
            'translation_fr': {
                'name': 'Tradução para Francês',
                'prompt': 'Traduza o seguinte texto para o francês de forma natural e fluente:'
            },
            'tone_formal': {
                'name': 'Tom Formal',
                'prompt': 'Reescreva o seguinte texto em português com um tom mais formal e profissional:'
            },
            'tone_casual': {
                'name': 'Tom Casual',
                'prompt': 'Reescreva o seguinte texto em português com um tom mais casual e descontraído:'
            },
            'text_expansion': {
                'name': 'Expandir Texto',
                'prompt': 'Expanda e elabore o seguinte texto em português, adicionando mais detalhes e informações relevantes:'
            },
            'explain_concept': {
                'name': 'Explicar Conceito',
                'prompt': 'Explique o seguinte conceito de forma simples e clara em português:'
            },
            'create_list': {
                'name': 'Criar Lista',
                'prompt': 'Transforme o seguinte texto em uma lista organizada e estruturada em português:'
            }
        }
    
    def _init_client(self):
        """Inicializa cliente OpenAI"""
        try:
            api_key = self.config_manager.get_api_key()
            if api_key:
                self.client = openai.OpenAI(api_key=api_key)
                logging.info("Cliente OpenAI inicializado")
            else:
                logging.warning("API key não configurada")
        except Exception as e:
            logging.error(f"Erro ao inicializar cliente OpenAI: {e}")
    
    def test_connection(self) -> bool:
        """Testa conexão com OpenAI"""
        try:
            if not self.client:
                self._init_client()
            
            if not self.client:
                return False
            
            # Fazer uma requisição simples para testar
            response = self.client.chat.completions.create(
                model=self.config_manager.get('OpenAI', 'model', 'gpt-3.5-turbo'),
                messages=[
                    {"role": "user", "content": "Teste de conexão. Responda apenas 'OK'."}
                ],
                max_tokens=10,
                temperature=0
            )
            
            return bool(response and response.choices)
            
        except Exception as e:
            logging.error(f"Erro no teste de conexão: {e}")
            return False
    
    def process_text(self, text: str, action: str, custom_prompt: str = None) -> Optional[str]:
        """Processa texto usando IA"""
        try:
            if not self.client:
                self._init_client()
            
            if not self.client:
                raise Exception("Cliente OpenAI não inicializado. Verifique a API key.")
            
            # Determinar prompt
            if custom_prompt:
                prompt = custom_prompt
            elif action in self.predefined_prompts:
                prompt = self.predefined_prompts[action]['prompt']
            else:
                raise Exception(f"Ação não reconhecida: {action}")
            
            # Preparar mensagens
            messages = [
                {
                    "role": "system",
                    "content": "Você é um assistente de texto especializado em português brasileiro. "
                              "Seja preciso, claro e mantenha o contexto original do texto."
                },
                {
                    "role": "user",
                    "content": f"{prompt}\n\nTexto:\n{text}"
                }
            ]
            
            # Configurações da API
            model = self.config_manager.get('OpenAI', 'model', 'gpt-3.5-turbo')
            max_tokens = int(self.config_manager.get('OpenAI', 'max_tokens', '500'))
            temperature = float(self.config_manager.get('OpenAI', 'temperature', '0.7'))
            
            # Fazer requisição
            logging.info(f"Processando texto com ação: {action}")

            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                timeout=30
            )
            
            if response and response.choices:
                result = response.choices[0].message.content.strip()
                logging.info(f"Resposta recebida: {len(result)} caracteres")
                return result
            else:
                raise Exception("Resposta vazia da API")
                
        except openai.error.AuthenticationError:
            logging.error("Erro de autenticação OpenAI - API key inválida")
            raise Exception("API key inválida. Verifique suas configurações.")
        except openai.error.RateLimitError:
            logging.error("Limite de taxa excedido")
            raise Exception("Limite de uso da API excedido. Tente novamente mais tarde.")
        except openai.error.APIConnectionError:
            logging.error("Erro de conexão com a API")
            raise Exception("Erro de conexão. Verifique sua internet.")
        except openai.error.InvalidRequestError as e:
            logging.error(f"Requisição inválida: {e}")
            raise Exception(f"Erro na requisição: {e}")
        except Exception as e:
            logging.error(f"Erro ao processar texto: {e}")
            raise
    
    def get_available_actions(self) -> Dict[str, str]:
        """Retorna ações disponíveis"""
        actions = {}
        
        # Ações predefinidas habilitadas
        if self.config_manager.get_boolean('Actions', 'grammar_correction', True):
            actions['grammar_correction'] = self.predefined_prompts['grammar_correction']['name']
        
        if self.config_manager.get_boolean('Actions', 'text_summarization', True):
            actions['text_summarization'] = self.predefined_prompts['text_summarization']['name']
        
        if self.config_manager.get_boolean('Actions', 'translation', True):
            actions['translation_en'] = self.predefined_prompts['translation_en']['name']
            actions['translation_es'] = self.predefined_prompts['translation_es']['name']
            actions['translation_fr'] = self.predefined_prompts['translation_fr']['name']
        
        if self.config_manager.get_boolean('Actions', 'tone_adjustment', True):
            actions['tone_formal'] = self.predefined_prompts['tone_formal']['name']
            actions['tone_casual'] = self.predefined_prompts['tone_casual']['name']
        
        if self.config_manager.get_boolean('Actions', 'text_expansion', True):
            actions['text_expansion'] = self.predefined_prompts['text_expansion']['name']
        
        # Ações adicionais
        actions['explain_concept'] = self.predefined_prompts['explain_concept']['name']
        actions['create_list'] = self.predefined_prompts['create_list']['name']
        
        # Prompts personalizados
        if self.config_manager.get_boolean('Actions', 'custom_prompts', True):
            custom_prompts = self.config_manager.get_custom_prompts()
            for name, prompt in custom_prompts.items():
                actions[f"custom_{name}"] = name
        
        return actions
    
    def process_custom_prompt(self, text: str, prompt_name: str) -> Optional[str]:
        """Processa texto com prompt personalizado"""
        try:
            custom_prompts = self.config_manager.get_custom_prompts()
            if prompt_name not in custom_prompts:
                raise Exception(f"Prompt personalizado não encontrado: {prompt_name}")
            
            custom_prompt = custom_prompts[prompt_name]
            return self.process_text(text, 'custom', custom_prompt)
            
        except Exception as e:
            logging.error(f"Erro ao processar prompt personalizado: {e}")
            raise
    
    def estimate_tokens(self, text: str) -> int:
        """Estima número de tokens do texto"""
        # Estimativa simples: ~4 caracteres por token
        return len(text) // 4
    
    def validate_text_length(self, text: str) -> bool:
        """Valida se o texto não é muito longo"""
        max_tokens = int(self.config_manager.get('OpenAI', 'max_tokens', '500'))
        estimated_tokens = self.estimate_tokens(text)
        
        # Reservar tokens para a resposta (metade do limite)
        max_input_tokens = max_tokens // 2
        
        return estimated_tokens <= max_input_tokens
    
    def get_model_info(self) -> Dict[str, Any]:
        """Retorna informações do modelo atual"""
        model = self.config_manager.get('OpenAI', 'model', 'gpt-3.5-turbo')
        max_tokens = self.config_manager.get('OpenAI', 'max_tokens', '500')
        temperature = self.config_manager.get('OpenAI', 'temperature', '0.7')
        
        return {
            'model': model,
            'max_tokens': max_tokens,
            'temperature': temperature,
            'api_key_configured': bool(self.config_manager.get_api_key())
        }
