@echo off
echo ========================================
echo  Assistente de Texto IA - Instalador
echo ========================================
echo.

REM Verificar se está executando como administrador
net session >nul 2>&1
if errorlevel 1 (
    echo Este instalador precisa ser executado como administrador.
    echo Clique com o botao direito e selecione "Executar como administrador"
    pause
    exit /b 1
)

echo [1/4] Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo Python nao encontrado. Baixando e instalando...
    echo.
    echo Baixe Python 3.9+ de: https://www.python.org/downloads/
    echo Certifique-se de marcar "Add Python to PATH" durante a instalacao.
    echo.
    echo Apos instalar Python, execute este script novamente.
    pause
    exit /b 1
)

echo [2/4] Instalando dependencias...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERRO: Falha ao instalar dependencias.
    pause
    exit /b 1
)

echo.
echo [3/4] Criando atalho na area de trabalho...
set DESKTOP=%USERPROFILE%\Desktop
set CURRENT_DIR=%~dp0
echo @echo off > "%DESKTOP%\Assistente de Texto IA.bat"
echo cd /d "%CURRENT_DIR%" >> "%DESKTOP%\Assistente de Texto IA.bat"
echo python main.py >> "%DESKTOP%\Assistente de Texto IA.bat"

echo.
echo [4/4] Configurando inicializacao automatica (opcional)...
set /p AUTO_START="Deseja que o aplicativo inicie automaticamente com o Windows? (s/n): "
if /i "%AUTO_START%"=="s" (
    set STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup
    copy "%DESKTOP%\Assistente de Texto IA.bat" "%STARTUP_FOLDER%\"
    echo Inicializacao automatica configurada.
) else (
    echo Inicializacao automatica nao configurada.
)

echo.
echo ========================================
echo  Instalacao concluida com sucesso!
echo ========================================
echo.
echo Para iniciar o aplicativo:
echo 1. Execute o atalho na area de trabalho, OU
echo 2. Execute: python main.py
echo.
echo Na primeira execucao, configure sua API key da OpenAI.
echo.
pause
