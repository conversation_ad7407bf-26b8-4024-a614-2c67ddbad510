#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Menu de Contexto
Exibe menu com ações de IA quando texto é selecionado
"""

import tkinter as tk
from tkinter import ttk
import sv_ttk
import threading
import logging
from typing import Callable
from .config_manager import ConfigManager
from .ai_client import AIClient

class ContextMenu:
    def __init__(self, config_manager: ConfigManager, ai_client: AIClient, 
                 selected_text: str, callback: Callable):
        self.config_manager = config_manager
        self.ai_client = ai_client
        self.selected_text = selected_text
        self.callback = callback
        self.window = None
        
    def show(self):
        """Exibe o menu de contexto"""
        try:
            self._create_menu()
        except Exception as e:
            logging.error(f"Erro ao exibir menu de contexto: {e}")
    
    def _create_menu(self):
        """Cria janela do menu de contexto"""
        self.window = tk.Tk()
        self.window.title("Assistente de Texto IA")
        self.window.geometry("300x400")
        self.window.resizable(<PERSON><PERSON><PERSON>, <PERSON>als<PERSON>)
        
        # Aplicar tema
        theme = self.config_manager.get('Interface', 'theme', 'dark')
        sv_ttk.set_theme(theme)
        
        # Posicionar próximo ao cursor
        self._position_near_cursor()
        
        # Manter janela sempre no topo
        self.window.attributes('-topmost', True)
        
        # Criar interface
        self._create_interface()
        
        # Configurar fechamento
        self.window.protocol("WM_DELETE_WINDOW", self._close_menu)
        self.window.bind("<Escape>", lambda e: self._close_menu())
        self.window.bind("<FocusOut>", lambda e: self._close_menu())
        
        # Focar na janela
        self.window.focus_force()
        
        # Executar loop
        self.window.mainloop()
    
    def _position_near_cursor(self):
        """Posiciona janela próxima ao cursor"""
        try:
            import win32gui
            cursor_pos = win32gui.GetCursorPos()
            
            # Ajustar posição para não sair da tela
            screen_width = self.window.winfo_screenwidth()
            screen_height = self.window.winfo_screenheight()
            
            x = min(cursor_pos[0] + 10, screen_width - 320)
            y = min(cursor_pos[1] + 10, screen_height - 420)
            
            self.window.geometry(f"300x400+{x}+{y}")
            
        except Exception as e:
            logging.warning(f"Erro ao posicionar menu: {e}")
            # Centralizar como fallback
            self.window.update_idletasks()
            x = (self.window.winfo_screenwidth() // 2) - 150
            y = (self.window.winfo_screenheight() // 2) - 200
            self.window.geometry(f"300x400+{x}+{y}")
    
    def _create_interface(self):
        """Cria interface do menu"""
        # Título
        title_frame = ttk.Frame(self.window)
        title_frame.pack(fill="x", padx=10, pady=10)
        
        ttk.Label(title_frame, text="Assistente de Texto IA", 
                 font=("", 12, "bold")).pack()
        
        # Prévia do texto selecionado
        preview_frame = ttk.LabelFrame(self.window, text="Texto Selecionado")
        preview_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        preview_text = self.selected_text[:100] + "..." if len(self.selected_text) > 100 else self.selected_text
        ttk.Label(preview_frame, text=preview_text, wraplength=280, 
                 justify="left").pack(padx=10, pady=10)
        
        # Ações disponíveis
        actions_frame = ttk.LabelFrame(self.window, text="Ações Disponíveis")
        actions_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # Criar botões para ações
        self._create_action_buttons(actions_frame)
        
        # Botões de controle
        control_frame = ttk.Frame(self.window)
        control_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        ttk.Button(control_frame, text="Cancelar", 
                  command=self._close_menu).pack(side="right")
        ttk.Button(control_frame, text="Configurações", 
                  command=self._open_settings).pack(side="right", padx=(0, 5))
    
    def _create_action_buttons(self, parent):
        """Cria botões das ações de IA"""
        available_actions = self.ai_client.get_available_actions()
        
        # Ações principais
        main_actions = [
            ('grammar_correction', 'Corrigir Gramática'),
            ('text_summarization', 'Resumir Texto'),
            ('translation_en', 'Traduzir para Inglês'),
            ('tone_formal', 'Tom Formal'),
            ('tone_casual', 'Tom Casual'),
            ('text_expansion', 'Expandir Texto')
        ]
        
        for action_id, default_name in main_actions:
            if action_id in available_actions:
                btn = ttk.Button(
                    parent, 
                    text=available_actions[action_id],
                    command=lambda aid=action_id: self._execute_action(aid)
                )
                btn.pack(fill="x", padx=10, pady=2)
        
        # Separador para traduções
        if any(action.startswith('translation_') for action in available_actions):
            ttk.Separator(parent, orient="horizontal").pack(fill="x", padx=10, pady=5)
            
            # Submenu de traduções
            translation_frame = ttk.Frame(parent)
            translation_frame.pack(fill="x", padx=10, pady=2)
            
            ttk.Label(translation_frame, text="Traduzir para:", 
                     font=("", 9)).pack(anchor="w")
            
            translations = [
                ('translation_en', 'Inglês'),
                ('translation_es', 'Espanhol'),
                ('translation_fr', 'Francês')
            ]
            
            for action_id, lang in translations:
                if action_id in available_actions:
                    btn = ttk.Button(
                        translation_frame,
                        text=lang,
                        command=lambda aid=action_id: self._execute_action(aid)
                    )
                    btn.pack(side="left", padx=(0, 5))
        
        # Prompts personalizados
        custom_prompts = self.config_manager.get_custom_prompts()
        if custom_prompts and self.config_manager.get_boolean('Actions', 'custom_prompts', True):
            ttk.Separator(parent, orient="horizontal").pack(fill="x", padx=10, pady=5)
            
            ttk.Label(parent, text="Prompts Personalizados:", 
                     font=("", 9)).pack(anchor="w", padx=10)
            
            for prompt_name in custom_prompts.keys():
                btn = ttk.Button(
                    parent,
                    text=prompt_name,
                    command=lambda pn=prompt_name: self._execute_custom_prompt(pn)
                )
                btn.pack(fill="x", padx=10, pady=2)
    
    def _execute_action(self, action_id: str):
        """Executa ação de IA"""
        try:
            self._close_menu()
            
            # Executar em thread separada para não bloquear UI
            thread = threading.Thread(
                target=self._process_ai_action,
                args=(action_id,),
                daemon=True
            )
            thread.start()
            
        except Exception as e:
            logging.error(f"Erro ao executar ação {action_id}: {e}")
    
    def _execute_custom_prompt(self, prompt_name: str):
        """Executa prompt personalizado"""
        try:
            self._close_menu()
            
            # Executar em thread separada
            thread = threading.Thread(
                target=self._process_custom_prompt,
                args=(prompt_name,),
                daemon=True
            )
            thread.start()
            
        except Exception as e:
            logging.error(f"Erro ao executar prompt personalizado {prompt_name}: {e}")
    
    def _process_ai_action(self, action_id: str):
        """Processa ação de IA em thread separada"""
        try:
            # Mostrar indicador de carregamento
            self._show_loading_indicator()
            
            # Processar texto
            response = self.ai_client.process_text(self.selected_text, action_id)
            
            if response:
                # Chamar callback com resultado
                self.callback(self.selected_text, response, action_id)
            else:
                self._show_error("Não foi possível processar o texto.")
                
        except Exception as e:
            logging.error(f"Erro no processamento da ação {action_id}: {e}")
            self._show_error(f"Erro: {str(e)}")
    
    def _process_custom_prompt(self, prompt_name: str):
        """Processa prompt personalizado em thread separada"""
        try:
            # Mostrar indicador de carregamento
            self._show_loading_indicator()
            
            # Processar com prompt personalizado
            response = self.ai_client.process_custom_prompt(self.selected_text, prompt_name)
            
            if response:
                # Chamar callback com resultado
                self.callback(self.selected_text, response, f"custom_{prompt_name}")
            else:
                self._show_error("Não foi possível processar o texto.")
                
        except Exception as e:
            logging.error(f"Erro no processamento do prompt {prompt_name}: {e}")
            self._show_error(f"Erro: {str(e)}")
    
    def _show_loading_indicator(self):
        """Exibe indicador de carregamento"""
        try:
            from .loading_window import LoadingWindow
            loading = LoadingWindow("Processando texto com IA...")
            loading.show()
        except ImportError:
            # Fallback simples
            logging.info("Processando texto com IA...")
    
    def _show_error(self, message: str):
        """Exibe mensagem de erro"""
        try:
            import tkinter.messagebox as messagebox
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Erro", message)
            root.destroy()
        except Exception as e:
            logging.error(f"Erro ao exibir mensagem: {e}")
    
    def _open_settings(self):
        """Abre janela de configurações"""
        try:
            self._close_menu()
            from .settings_window import SettingsWindow
            settings = SettingsWindow(self.config_manager)
            settings.show()
        except Exception as e:
            logging.error(f"Erro ao abrir configurações: {e}")
    
    def _close_menu(self):
        """Fecha o menu de contexto"""
        if self.window:
            try:
                self.window.destroy()
            except:
                pass
            self.window = None
