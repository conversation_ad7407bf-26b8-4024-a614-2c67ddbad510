#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Janela de Configurações
Interface gráfica para configurar a aplicação com tema Sun Valley
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import sv_ttk
import logging
from .config_manager import ConfigManager

class SettingsWindow:
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.window = None
        self.notebook = None
        
        # Variáveis de controle
        self.api_key_var = tk.StringVar()
        self.model_var = tk.StringVar()
        self.max_tokens_var = tk.StringVar()
        self.temperature_var = tk.StringVar()
        self.theme_var = tk.StringVar()
        self.notifications_var = tk.BooleanVar()
        self.auto_start_var = tk.BooleanVar()
        
        # Variáveis de ações
        self.grammar_var = tk.BooleanVar()
        self.summary_var = tk.BooleanVar()
        self.translation_var = tk.BooleanVar()
        self.tone_var = tk.BooleanVar()
        self.expansion_var = tk.BooleanVar()
        self.custom_prompts_var = tk.BooleanVar()
        
        # Variáveis de atalhos
        self.shortcut_vars = {}
        
    def show(self):
        """Exibe a janela de configurações"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            self.window.focus_force()
            return
        
        self._create_window()
        self._load_current_settings()
        self.window.mainloop()
    
    def _create_window(self):
        """Cria a janela principal"""
        self.window = tk.Tk()
        self.window.title("Configurações - Assistente de Texto IA")
        self.window.geometry("600x500")
        self.window.resizable(True, True)
        
        # Aplicar tema Sun Valley
        sv_ttk.set_theme("dark")
        
        # Centralizar janela
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.window.winfo_screenheight() // 2) - (500 // 2)
        self.window.geometry(f"600x500+{x}+{y}")
        
        # Criar notebook (abas)
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Criar abas
        self._create_openai_tab()
        self._create_interface_tab()
        self._create_actions_tab()
        self._create_shortcuts_tab()
        self._create_prompts_tab()
        
        # Botões de ação
        self._create_action_buttons()
        
        # Configurar fechamento da janela
        self.window.protocol("WM_DELETE_WINDOW", self._on_close)
    
    def _create_openai_tab(self):
        """Cria aba de configurações da OpenAI"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="OpenAI")
        
        # API Key
        ttk.Label(frame, text="API Key da OpenAI:").pack(anchor="w", padx=10, pady=(10, 5))
        api_key_frame = ttk.Frame(frame)
        api_key_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        self.api_key_entry = ttk.Entry(api_key_frame, textvariable=self.api_key_var, show="*", width=50)
        self.api_key_entry.pack(side="left", fill="x", expand=True)
        
        ttk.Button(api_key_frame, text="Mostrar", command=self._toggle_api_key_visibility).pack(side="right", padx=(5, 0))
        
        # Modelo
        ttk.Label(frame, text="Modelo:").pack(anchor="w", padx=10, pady=(10, 5))
        model_combo = ttk.Combobox(frame, textvariable=self.model_var, values=[
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k",
            "gpt-4",
            "gpt-4-32k"
        ], state="readonly")
        model_combo.pack(fill="x", padx=10, pady=(0, 10))
        
        # Max Tokens
        ttk.Label(frame, text="Máximo de Tokens:").pack(anchor="w", padx=10, pady=(10, 5))
        tokens_frame = ttk.Frame(frame)
        tokens_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        ttk.Entry(tokens_frame, textvariable=self.max_tokens_var, width=10).pack(side="left")
        ttk.Label(tokens_frame, text="(100-4000)").pack(side="left", padx=(5, 0))
        
        # Temperature
        ttk.Label(frame, text="Temperatura (Criatividade):").pack(anchor="w", padx=10, pady=(10, 5))
        temp_frame = ttk.Frame(frame)
        temp_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        ttk.Entry(temp_frame, textvariable=self.temperature_var, width=10).pack(side="left")
        ttk.Label(temp_frame, text="(0.0-2.0)").pack(side="left", padx=(5, 0))
        
        # Teste de conexão
        ttk.Button(frame, text="Testar Conexão", command=self._test_connection).pack(pady=20)
    
    def _create_interface_tab(self):
        """Cria aba de configurações da interface"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Interface")
        
        # Tema
        ttk.Label(frame, text="Tema:").pack(anchor="w", padx=10, pady=(10, 5))
        theme_combo = ttk.Combobox(frame, textvariable=self.theme_var, values=[
            "dark", "light"
        ], state="readonly")
        theme_combo.pack(fill="x", padx=10, pady=(0, 10))
        theme_combo.bind("<<ComboboxSelected>>", self._on_theme_change)
        
        # Notificações
        ttk.Checkbutton(
            frame, 
            text="Mostrar notificações do sistema", 
            variable=self.notifications_var
        ).pack(anchor="w", padx=10, pady=10)
        
        # Auto iniciar
        ttk.Checkbutton(
            frame, 
            text="Iniciar monitoramento automaticamente", 
            variable=self.auto_start_var
        ).pack(anchor="w", padx=10, pady=10)
        
        # Informações
        info_frame = ttk.LabelFrame(frame, text="Informações")
        info_frame.pack(fill="both", expand=True, padx=10, pady=20)
        
        info_text = """
Assistente de Texto com IA v1.0.0

Como usar:
1. Configure sua API key da OpenAI
2. Selecione texto em qualquer aplicação
3. Use Ctrl+Shift+A para abrir o menu de ações
4. Ou use atalhos rápidos para ações específicas

Atalhos padrão:
• Ctrl+Shift+A - Menu de ações
• Ctrl+Shift+C - Correção rápida
• Ctrl+Shift+T - Tradução rápida
• Ctrl+Shift+S - Resumo rápido
        """
        
        ttk.Label(info_frame, text=info_text, justify="left").pack(padx=10, pady=10)
    
    def _create_actions_tab(self):
        """Cria aba de configurações das ações"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Ações")
        
        ttk.Label(frame, text="Ações Disponíveis:", font=("", 12, "bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        # Ações principais
        ttk.Checkbutton(frame, text="Correção Gramatical", variable=self.grammar_var).pack(anchor="w", padx=20, pady=5)
        ttk.Checkbutton(frame, text="Resumo de Texto", variable=self.summary_var).pack(anchor="w", padx=20, pady=5)
        ttk.Checkbutton(frame, text="Tradução", variable=self.translation_var).pack(anchor="w", padx=20, pady=5)
        ttk.Checkbutton(frame, text="Ajuste de Tom", variable=self.tone_var).pack(anchor="w", padx=20, pady=5)
        ttk.Checkbutton(frame, text="Expansão de Texto", variable=self.expansion_var).pack(anchor="w", padx=20, pady=5)
        ttk.Checkbutton(frame, text="Prompts Personalizados", variable=self.custom_prompts_var).pack(anchor="w", padx=20, pady=5)
    
    def _create_shortcuts_tab(self):
        """Cria aba de configurações dos atalhos"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Atalhos")
        
        ttk.Label(frame, text="Atalhos de Teclado:", font=("", 12, "bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        shortcuts = [
            ("activate_menu", "Menu de Ações"),
            ("quick_correct", "Correção Rápida"),
            ("quick_translate", "Tradução Rápida"),
            ("quick_summarize", "Resumo Rápido")
        ]
        
        for key, label in shortcuts:
            shortcut_frame = ttk.Frame(frame)
            shortcut_frame.pack(fill="x", padx=10, pady=5)
            
            ttk.Label(shortcut_frame, text=f"{label}:", width=20).pack(side="left")
            
            var = tk.StringVar()
            self.shortcut_vars[key] = var
            ttk.Entry(shortcut_frame, textvariable=var, width=20).pack(side="left", padx=(10, 0))
    
    def _create_prompts_tab(self):
        """Cria aba de prompts personalizados"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Prompts")
        
        # Lista de prompts
        ttk.Label(frame, text="Prompts Personalizados:", font=("", 12, "bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        # Frame para lista e botões
        list_frame = ttk.Frame(frame)
        list_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Listbox com prompts
        self.prompts_listbox = tk.Listbox(list_frame, height=8)
        self.prompts_listbox.pack(side="left", fill="both", expand=True)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.prompts_listbox.yview)
        scrollbar.pack(side="right", fill="y")
        self.prompts_listbox.config(yscrollcommand=scrollbar.set)
        
        # Botões
        buttons_frame = ttk.Frame(frame)
        buttons_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        ttk.Button(buttons_frame, text="Adicionar", command=self._add_prompt).pack(side="left", padx=(0, 5))
        ttk.Button(buttons_frame, text="Editar", command=self._edit_prompt).pack(side="left", padx=5)
        ttk.Button(buttons_frame, text="Remover", command=self._remove_prompt).pack(side="left", padx=5)
    
    def _create_action_buttons(self):
        """Cria botões de ação da janela"""
        buttons_frame = ttk.Frame(self.window)
        buttons_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        ttk.Button(buttons_frame, text="Cancelar", command=self._on_close).pack(side="right", padx=(5, 0))
        ttk.Button(buttons_frame, text="Aplicar", command=self._apply_settings).pack(side="right")
        ttk.Button(buttons_frame, text="OK", command=self._save_and_close).pack(side="right", padx=(0, 5))
    
    def _load_current_settings(self):
        """Carrega configurações atuais"""
        # OpenAI
        self.api_key_var.set(self.config_manager.get_api_key())
        self.model_var.set(self.config_manager.get('OpenAI', 'model', 'gpt-3.5-turbo'))
        self.max_tokens_var.set(self.config_manager.get('OpenAI', 'max_tokens', '500'))
        self.temperature_var.set(self.config_manager.get('OpenAI', 'temperature', '0.7'))
        
        # Interface
        self.theme_var.set(self.config_manager.get('Interface', 'theme', 'dark'))
        self.notifications_var.set(self.config_manager.get_boolean('Interface', 'show_notifications', True))
        self.auto_start_var.set(self.config_manager.get_boolean('Interface', 'auto_start', False))
        
        # Ações
        self.grammar_var.set(self.config_manager.get_boolean('Actions', 'grammar_correction', True))
        self.summary_var.set(self.config_manager.get_boolean('Actions', 'text_summarization', True))
        self.translation_var.set(self.config_manager.get_boolean('Actions', 'translation', True))
        self.tone_var.set(self.config_manager.get_boolean('Actions', 'tone_adjustment', True))
        self.expansion_var.set(self.config_manager.get_boolean('Actions', 'text_expansion', True))
        self.custom_prompts_var.set(self.config_manager.get_boolean('Actions', 'custom_prompts', True))
        
        # Atalhos
        for key in self.shortcut_vars:
            value = self.config_manager.get('Shortcuts', key, '')
            self.shortcut_vars[key].set(value)
        
        # Prompts personalizados
        self._load_custom_prompts()
    
    def _load_custom_prompts(self):
        """Carrega prompts personalizados na lista"""
        self.prompts_listbox.delete(0, tk.END)
        custom_prompts = self.config_manager.get_custom_prompts()
        for name in custom_prompts.keys():
            self.prompts_listbox.insert(tk.END, name)
    
    def _toggle_api_key_visibility(self):
        """Alterna visibilidade da API key"""
        if self.api_key_entry.cget("show") == "*":
            self.api_key_entry.config(show="")
        else:
            self.api_key_entry.config(show="*")
    
    def _on_theme_change(self, event=None):
        """Callback para mudança de tema"""
        theme = self.theme_var.get()
        sv_ttk.set_theme(theme)
    
    def _test_connection(self):
        """Testa conexão com OpenAI"""
        try:
            # Salvar temporariamente a API key
            old_key = self.config_manager.get_api_key()
            self.config_manager.set_api_key(self.api_key_var.get())
            
            # Testar conexão
            from .ai_client import AIClient
            client = AIClient(self.config_manager)
            success = client.test_connection()
            
            # Restaurar API key anterior se o teste falhou
            if not success:
                self.config_manager.set_api_key(old_key)
                messagebox.showerror("Erro", "Falha na conexão com OpenAI. Verifique sua API key.")
            else:
                messagebox.showinfo("Sucesso", "Conexão com OpenAI estabelecida com sucesso!")
                
        except Exception as e:
            messagebox.showerror("Erro", f"Erro no teste de conexão: {e}")
    
    def _add_prompt(self):
        """Adiciona novo prompt personalizado"""
        from .prompt_editor import PromptEditor
        editor = PromptEditor(self.window, self.config_manager)
        if editor.show():
            self._load_custom_prompts()
    
    def _edit_prompt(self):
        """Edita prompt selecionado"""
        selection = self.prompts_listbox.curselection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione um prompt para editar.")
            return
        
        prompt_name = self.prompts_listbox.get(selection[0])
        from .prompt_editor import PromptEditor
        editor = PromptEditor(self.window, self.config_manager, prompt_name)
        if editor.show():
            self._load_custom_prompts()
    
    def _remove_prompt(self):
        """Remove prompt selecionado"""
        selection = self.prompts_listbox.curselection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione um prompt para remover.")
            return

        prompt_name = self.prompts_listbox.get(selection[0])
        if messagebox.askyesno("Confirmar", f"Remover o prompt '{prompt_name}'?"):
            # Implementar remoção de prompt
            self.config_manager.remove_custom_prompt(prompt_name)
            self._load_custom_prompts()
    
    def _apply_settings(self):
        """Aplica configurações sem fechar janela"""
        self._save_settings()
    
    def _save_and_close(self):
        """Salva configurações e fecha janela"""
        self._save_settings()
        self._on_close()
    
    def _save_settings(self):
        """Salva todas as configurações"""
        try:
            # OpenAI
            self.config_manager.set_api_key(self.api_key_var.get())
            self.config_manager.set('OpenAI', 'model', self.model_var.get())
            self.config_manager.set('OpenAI', 'max_tokens', self.max_tokens_var.get())
            self.config_manager.set('OpenAI', 'temperature', self.temperature_var.get())
            
            # Interface
            self.config_manager.set('Interface', 'theme', self.theme_var.get())
            self.config_manager.set('Interface', 'show_notifications', str(self.notifications_var.get()))
            self.config_manager.set('Interface', 'auto_start', str(self.auto_start_var.get()))
            
            # Ações
            self.config_manager.set('Actions', 'grammar_correction', str(self.grammar_var.get()))
            self.config_manager.set('Actions', 'text_summarization', str(self.summary_var.get()))
            self.config_manager.set('Actions', 'translation', str(self.translation_var.get()))
            self.config_manager.set('Actions', 'tone_adjustment', str(self.tone_var.get()))
            self.config_manager.set('Actions', 'text_expansion', str(self.expansion_var.get()))
            self.config_manager.set('Actions', 'custom_prompts', str(self.custom_prompts_var.get()))
            
            # Atalhos
            for key, var in self.shortcut_vars.items():
                self.config_manager.set('Shortcuts', key, var.get())
            
            messagebox.showinfo("Sucesso", "Configurações salvas com sucesso!")
            
        except Exception as e:
            logging.error(f"Erro ao salvar configurações: {e}")
            messagebox.showerror("Erro", f"Erro ao salvar configurações: {e}")
    
    def _on_close(self):
        """Callback para fechamento da janela"""
        if self.window:
            self.window.destroy()
            self.window = None
