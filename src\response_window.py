#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Janela de Resposta da IA
Exibe resposta da IA e permite ações como copiar ou substituir texto
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import sv_ttk
import win32clipboard
import win32con
import win32api
import win32gui
import logging
from .config_manager import ConfigManager

class ResponseWindow:
    def __init__(self, original_text: str, ai_response: str, action: str, config_manager: ConfigManager):
        self.original_text = original_text
        self.ai_response = ai_response
        self.action = action
        self.config_manager = config_manager
        self.window = None
        
    def show(self):
        """Exibe a janela de resposta"""
        try:
            self._create_window()
        except Exception as e:
            logging.error(f"Erro ao exibir janela de resposta: {e}")
    
    def _create_window(self):
        """Cria janela de resposta"""
        self.window = tk.Tk()
        self.window.title("Resposta da IA - Assistente de Texto")
        self.window.geometry("700x500")
        self.window.resizable(True, True)
        
        # Aplicar tema
        theme = self.config_manager.get('Interface', 'theme', 'dark')
        sv_ttk.set_theme(theme)
        
        # Centralizar janela
        self._center_window()
        
        # Manter janela sempre no topo
        self.window.attributes('-topmost', True)
        
        # Criar interface
        self._create_interface()
        
        # Configurar fechamento
        self.window.protocol("WM_DELETE_WINDOW", self._close_window)
        self.window.bind("<Escape>", lambda e: self._close_window())
        
        # Focar na janela
        self.window.focus_force()
        
        # Executar loop
        self.window.mainloop()
    
    def _center_window(self):
        """Centraliza janela na tela"""
        self.window.update_idletasks()
        width = 700
        height = 500
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_interface(self):
        """Cria interface da janela"""
        # Título
        title_frame = ttk.Frame(self.window)
        title_frame.pack(fill="x", padx=10, pady=10)
        
        action_name = self._get_action_name()
        ttk.Label(title_frame, text=f"Resultado: {action_name}", 
                 font=("", 14, "bold")).pack()
        
        # Notebook para abas
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # Aba de comparação
        self._create_comparison_tab(notebook)
        
        # Aba de resposta completa
        self._create_response_tab(notebook)
        
        # Botões de ação
        self._create_action_buttons()
    
    def _create_comparison_tab(self, notebook):
        """Cria aba de comparação lado a lado"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Comparação")
        
        # Frame principal com duas colunas
        main_frame = ttk.Frame(frame)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Coluna esquerda - Texto original
        left_frame = ttk.LabelFrame(main_frame, text="Texto Original")
        left_frame.pack(side="left", fill="both", expand=True, padx=(0, 5))
        
        self.original_text_widget = scrolledtext.ScrolledText(
            left_frame, 
            wrap=tk.WORD, 
            height=15,
            state="normal"
        )
        self.original_text_widget.pack(fill="both", expand=True, padx=10, pady=10)
        self.original_text_widget.insert("1.0", self.original_text)
        self.original_text_widget.config(state="disabled")
        
        # Coluna direita - Resposta da IA
        right_frame = ttk.LabelFrame(main_frame, text="Resposta da IA")
        right_frame.pack(side="right", fill="both", expand=True, padx=(5, 0))
        
        self.ai_response_widget = scrolledtext.ScrolledText(
            right_frame, 
            wrap=tk.WORD, 
            height=15,
            state="normal"
        )
        self.ai_response_widget.pack(fill="both", expand=True, padx=10, pady=10)
        self.ai_response_widget.insert("1.0", self.ai_response)
        self.ai_response_widget.config(state="disabled")
    
    def _create_response_tab(self, notebook):
        """Cria aba com resposta completa editável"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Editar Resposta")
        
        # Instruções
        instructions = ttk.Label(
            frame, 
            text="Você pode editar a resposta da IA antes de copiar ou substituir:",
            font=("", 10)
        )
        instructions.pack(anchor="w", padx=10, pady=(10, 5))
        
        # Área de texto editável
        self.editable_response_widget = scrolledtext.ScrolledText(
            frame, 
            wrap=tk.WORD, 
            height=20,
            state="normal"
        )
        self.editable_response_widget.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        self.editable_response_widget.insert("1.0", self.ai_response)
        
        # Focar no texto editável
        self.editable_response_widget.focus_set()
    
    def _create_action_buttons(self):
        """Cria botões de ação"""
        buttons_frame = ttk.Frame(self.window)
        buttons_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # Botões principais
        ttk.Button(
            buttons_frame, 
            text="Copiar Resposta", 
            command=self._copy_response
        ).pack(side="left", padx=(0, 5))
        
        ttk.Button(
            buttons_frame, 
            text="Substituir Texto Original", 
            command=self._replace_text
        ).pack(side="left", padx=5)
        
        ttk.Button(
            buttons_frame, 
            text="Copiar Original", 
            command=self._copy_original
        ).pack(side="left", padx=5)
        
        # Botões de controle
        ttk.Button(
            buttons_frame, 
            text="Fechar", 
            command=self._close_window
        ).pack(side="right")
        
        ttk.Button(
            buttons_frame, 
            text="Nova Ação", 
            command=self._new_action
        ).pack(side="right", padx=(0, 5))
    
    def _get_action_name(self) -> str:
        """Obtém nome amigável da ação"""
        action_names = {
            'grammar_correction': 'Correção Gramatical',
            'text_summarization': 'Resumo de Texto',
            'translation_en': 'Tradução para Inglês',
            'translation_es': 'Tradução para Espanhol',
            'translation_fr': 'Tradução para Francês',
            'tone_formal': 'Tom Formal',
            'tone_casual': 'Tom Casual',
            'text_expansion': 'Expansão de Texto',
            'explain_concept': 'Explicação de Conceito',
            'create_list': 'Criação de Lista'
        }
        
        if self.action.startswith('custom_'):
            return f"Prompt Personalizado: {self.action[7:]}"
        
        return action_names.get(self.action, self.action.replace('_', ' ').title())
    
    def _copy_response(self):
        """Copia resposta da IA para área de transferência"""
        try:
            # Obter texto editado se disponível
            response_text = self.editable_response_widget.get("1.0", tk.END).strip()
            
            # Copiar para área de transferência
            self._copy_to_clipboard(response_text)
            
            # Mostrar confirmação
            self._show_notification("Resposta copiada para a área de transferência!")
            
        except Exception as e:
            logging.error(f"Erro ao copiar resposta: {e}")
            messagebox.showerror("Erro", f"Erro ao copiar resposta: {e}")
    
    def _copy_original(self):
        """Copia texto original para área de transferência"""
        try:
            self._copy_to_clipboard(self.original_text)
            self._show_notification("Texto original copiado para a área de transferência!")
            
        except Exception as e:
            logging.error(f"Erro ao copiar texto original: {e}")
            messagebox.showerror("Erro", f"Erro ao copiar texto original: {e}")
    
    def _replace_text(self):
        """Substitui texto original pela resposta da IA"""
        try:
            # Obter texto editado
            response_text = self.editable_response_widget.get("1.0", tk.END).strip()
            
            # Confirmar substituição
            if not messagebox.askyesno(
                "Confirmar Substituição", 
                "Deseja substituir o texto original pela resposta da IA?\n\n"
                "Esta ação irá:\n"
                "1. Copiar a resposta para a área de transferência\n"
                "2. Simular Ctrl+V para colar o texto\n\n"
                "Certifique-se de que o cursor está na posição correta."
            ):
                return
            
            # Copiar resposta para área de transferência
            self._copy_to_clipboard(response_text)
            
            # Fechar janela
            self._close_window()
            
            # Aguardar um pouco para a janela fechar
            import time
            time.sleep(0.2)
            
            # Simular Ctrl+V para colar
            self._paste_text()
            
            # Mostrar notificação
            self._show_notification("Texto substituído com sucesso!")
            
        except Exception as e:
            logging.error(f"Erro ao substituir texto: {e}")
            messagebox.showerror("Erro", f"Erro ao substituir texto: {e}")
    
    def _copy_to_clipboard(self, text: str):
        """Copia texto para área de transferência"""
        try:
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardText(text, win32con.CF_UNICODETEXT)
            win32clipboard.CloseClipboard()
        except Exception as e:
            try:
                win32clipboard.CloseClipboard()
            except:
                pass
            raise e
    
    def _paste_text(self):
        """Simula Ctrl+V para colar texto"""
        try:
            # Simular Ctrl+V
            win32api.keybd_event(win32con.VK_CONTROL, 0, 0, 0)
            win32api.keybd_event(ord('V'), 0, 0, 0)
            win32api.keybd_event(ord('V'), 0, win32con.KEYEVENTF_KEYUP, 0)
            win32api.keybd_event(win32con.VK_CONTROL, 0, win32con.KEYEVENTF_KEYUP, 0)
        except Exception as e:
            logging.error(f"Erro ao colar texto: {e}")
    
    def _show_notification(self, message: str):
        """Exibe notificação"""
        try:
            if self.config_manager.get_boolean('Interface', 'show_notifications', True):
                # Usar messagebox como notificação simples
                messagebox.showinfo("Sucesso", message)
        except Exception as e:
            logging.error(f"Erro ao exibir notificação: {e}")
    
    def _new_action(self):
        """Abre menu para nova ação com o texto original"""
        try:
            self._close_window()
            
            # Importar e mostrar menu de contexto novamente
            from .context_menu import ContextMenu
            from .ai_client import AIClient
            
            ai_client = AIClient(self.config_manager)
            context_menu = ContextMenu(
                self.config_manager,
                ai_client,
                self.original_text,
                self._on_new_response
            )
            context_menu.show()
            
        except Exception as e:
            logging.error(f"Erro ao abrir nova ação: {e}")
    
    def _on_new_response(self, original_text: str, ai_response: str, action: str):
        """Callback para nova resposta da IA"""
        try:
            # Criar nova janela de resposta
            new_window = ResponseWindow(original_text, ai_response, action, self.config_manager)
            new_window.show()
        except Exception as e:
            logging.error(f"Erro ao processar nova resposta: {e}")
    
    def _close_window(self):
        """Fecha a janela"""
        if self.window:
            try:
                self.window.destroy()
            except:
                pass
            self.window = None
