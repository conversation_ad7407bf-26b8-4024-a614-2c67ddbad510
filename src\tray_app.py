#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Aplicação de Bandeja do Sistema
Gerencia a aplicação principal que roda na bandeja do Windows
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import pystray
from PIL import Image, ImageDraw
import threading
import logging
from .config_manager import ConfigManager

class TrayApp:
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.icon = None
        self.text_monitor = None
        self.ai_client = None
        self.running = False
        
        # Inicializar componentes
        self._init_components()
    
    def _init_components(self):
        """Inicializa componentes da aplicação"""
        try:
            # Importar e inicializar monitor de texto
            from .text_monitor import TextMonitor
            self.text_monitor = TextMonitor(self.config_manager, self._on_text_selected)
            
            # Importar e inicializar cliente AI
            from .ai_client import AIClient
            self.ai_client = AIClient(self.config_manager)
            
        except Exception as e:
            logging.error(f"Erro ao inicializar componentes: {e}")
    
    def _create_icon_image(self):
        """Cria ícone para a bandeja do sistema"""
        try:
            # Tentar carregar ícone personalizado
            icon_path = "assets/icon.png"
            if os.path.exists(icon_path):
                return Image.open(icon_path)
        except Exception as e:
            logging.warning(f"Não foi possível carregar ícone personalizado: {e}")
        
        # Criar ícone simples programaticamente
        width = 64
        height = 64
        image = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        # Desenhar ícone simples (círculo com "AI")
        draw.ellipse([8, 8, width-8, height-8], fill=(0, 120, 215, 255), outline=(255, 255, 255, 255), width=2)
        
        # Adicionar texto "AI" (simplificado)
        draw.rectangle([20, 25, 25, 40], fill=(255, 255, 255, 255))  # A
        draw.rectangle([22, 30, 23, 35], fill=(0, 120, 215, 255))    # A hole
        draw.rectangle([30, 25, 32, 40], fill=(255, 255, 255, 255))  # I
        draw.rectangle([35, 25, 37, 40], fill=(255, 255, 255, 255))  # I
        
        return image
    
    def _create_menu(self):
        """Cria menu da bandeja do sistema"""
        return pystray.Menu(
            pystray.MenuItem(
                "Assistente de Texto IA",
                self._show_about,
                default=True
            ),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem(
                "Configurações",
                self._show_settings
            ),
            pystray.MenuItem(
                "Ativar/Desativar Monitoramento",
                self._toggle_monitoring,
                checked=lambda item: self.text_monitor and self.text_monitor.is_active()
            ),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem(
                "Testar Conexão OpenAI",
                self._test_openai_connection
            ),
            pystray.MenuItem(
                "Visualizar Log",
                self._show_log
            ),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem(
                "Sair",
                self._quit_application
            )
        )
    
    def _show_about(self, icon=None, item=None):
        """Exibe informações sobre a aplicação"""
        def show_dialog():
            root = tk.Tk()
            root.withdraw()
            messagebox.showinfo(
                "Assistente de Texto IA",
                "Assistente de Texto com Inteligência Artificial\n\n"
                "Versão: 1.0.0\n"
                "Desenvolvido com Python e OpenAI\n\n"
                "Selecione texto em qualquer aplicação e use Ctrl+Shift+A\n"
                "para acessar as funcionalidades de IA."
            )
            root.destroy()
        
        threading.Thread(target=show_dialog, daemon=True).start()
    
    def _show_settings(self, icon=None, item=None):
        """Abre janela de configurações"""
        def open_settings():
            try:
                from .settings_window import SettingsWindow
                settings = SettingsWindow(self.config_manager)
                settings.show()
            except Exception as e:
                logging.error(f"Erro ao abrir configurações: {e}")
                root = tk.Tk()
                root.withdraw()
                messagebox.showerror("Erro", f"Erro ao abrir configurações: {e}")
                root.destroy()
        
        threading.Thread(target=open_settings, daemon=True).start()
    
    def _toggle_monitoring(self, icon=None, item=None):
        """Ativa/desativa monitoramento de texto"""
        if self.text_monitor:
            if self.text_monitor.is_active():
                self.text_monitor.stop()
                self._show_notification("Monitoramento desativado")
            else:
                self.text_monitor.start()
                self._show_notification("Monitoramento ativado")
    
    def _test_openai_connection(self, icon=None, item=None):
        """Testa conexão com OpenAI"""
        def test_connection():
            try:
                if not self.ai_client:
                    raise Exception("Cliente AI não inicializado")
                
                # Teste simples
                response = self.ai_client.test_connection()
                
                root = tk.Tk()
                root.withdraw()
                if response:
                    messagebox.showinfo("Teste de Conexão", "Conexão com OpenAI estabelecida com sucesso!")
                else:
                    messagebox.showerror("Teste de Conexão", "Falha na conexão com OpenAI. Verifique sua API key.")
                root.destroy()
                
            except Exception as e:
                logging.error(f"Erro no teste de conexão: {e}")
                root = tk.Tk()
                root.withdraw()
                messagebox.showerror("Erro", f"Erro no teste de conexão: {e}")
                root.destroy()
        
        threading.Thread(target=test_connection, daemon=True).start()
    
    def _show_log(self, icon=None, item=None):
        """Abre arquivo de log"""
        try:
            import subprocess
            log_file = "ai_text_assistant.log"
            if os.path.exists(log_file):
                subprocess.run(["notepad.exe", log_file])
            else:
                root = tk.Tk()
                root.withdraw()
                messagebox.showinfo("Log", "Arquivo de log não encontrado.")
                root.destroy()
        except Exception as e:
            logging.error(f"Erro ao abrir log: {e}")
    
    def _quit_application(self, icon=None, item=None):
        """Encerra a aplicação"""
        self.cleanup()
        if self.icon:
            self.icon.stop()
        sys.exit(0)
    
    def _show_notification(self, message: str, title: str = "Assistente de Texto IA"):
        """Exibe notificação do sistema"""
        try:
            if self.config_manager.get_boolean('Interface', 'show_notifications', True):
                if self.icon:
                    self.icon.notify(message, title)
        except Exception as e:
            logging.error(f"Erro ao exibir notificação: {e}")
    
    def _on_text_selected(self, selected_text: str):
        """Callback chamado quando texto é selecionado"""
        try:
            logging.info(f"Texto selecionado: {selected_text[:50]}...")
            
            # Importar e mostrar menu de contexto
            from .context_menu import ContextMenu
            context_menu = ContextMenu(
                self.config_manager,
                self.ai_client,
                selected_text,
                self._on_ai_response
            )
            context_menu.show()
            
        except Exception as e:
            logging.error(f"Erro ao processar texto selecionado: {e}")
    
    def _on_ai_response(self, original_text: str, ai_response: str, action: str):
        """Callback chamado quando IA responde"""
        try:
            from .response_window import ResponseWindow
            response_window = ResponseWindow(
                original_text,
                ai_response,
                action,
                self.config_manager
            )
            response_window.show()
            
        except Exception as e:
            logging.error(f"Erro ao exibir resposta da IA: {e}")
    
    def run(self):
        """Inicia a aplicação"""
        try:
            self.running = True
            
            # Criar ícone da bandeja
            image = self._create_icon_image()
            menu = self._create_menu()
            
            self.icon = pystray.Icon(
                "ai_text_assistant",
                image,
                "Assistente de Texto IA",
                menu
            )
            
            # Iniciar monitoramento de texto se configurado
            if self.text_monitor and self.config_manager.get_boolean('Interface', 'auto_start', False):
                self.text_monitor.start()
                self._show_notification("Assistente de Texto IA iniciado")
            
            # Executar aplicação
            logging.info("Iniciando aplicação na bandeja do sistema")
            self.icon.run()
            
        except Exception as e:
            logging.error(f"Erro ao executar aplicação: {e}")
            raise
    
    def cleanup(self):
        """Limpa recursos da aplicação"""
        try:
            self.running = False
            
            if self.text_monitor:
                self.text_monitor.stop()
            
            logging.info("Aplicação encerrada")
            
        except Exception as e:
            logging.error(f"Erro durante limpeza: {e}")
