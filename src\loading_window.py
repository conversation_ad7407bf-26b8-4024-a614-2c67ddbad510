#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Janela de Carregamento
Exibe indicador de progresso durante processamento da IA
"""

import tkinter as tk
from tkinter import ttk
import sv_ttk
import threading
import time

class LoadingWindow:
    def __init__(self, message: str = "Processando..."):
        self.message = message
        self.window = None
        self.running = False
    
    def show(self):
        """Exibe janela de carregamento"""
        try:
            self.running = True
            self._create_window()
            
            # Iniciar animação
            self._animate()
            
        except Exception as e:
            print(f"Erro na janela de carregamento: {e}")
    
    def hide(self):
        """Oculta janela de carregamento"""
        self.running = False
        if self.window:
            try:
                self.window.destroy()
            except:
                pass
            self.window = None
    
    def _create_window(self):
        """Cria janela de carregamento"""
        self.window = tk.Tk()
        self.window.title("Processando...")
        self.window.geometry("300x150")
        self.window.resizable(False, False)
        
        # Aplicar tema
        sv_ttk.set_theme("dark")
        
        # Centralizar
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - 150
        y = (self.window.winfo_screenheight() // 2) - 75
        self.window.geometry(f"300x150+{x}+{y}")
        
        # Remover decorações
        self.window.overrideredirect(True)
        
        # Manter no topo
        self.window.attributes('-topmost', True)
        
        # Frame principal
        main_frame = ttk.Frame(self.window, padding=20)
        main_frame.pack(fill="both", expand=True)
        
        # Mensagem
        ttk.Label(main_frame, text=self.message, font=("", 11)).pack(pady=(10, 20))
        
        # Barra de progresso
        self.progress = ttk.Progressbar(main_frame, mode="indeterminate")
        self.progress.pack(fill="x", pady=(0, 10))
        
        # Iniciar progresso
        self.progress.start(10)
    
    def _animate(self):
        """Anima a janela de carregamento"""
        if self.running and self.window:
            try:
                self.window.update()
                self.window.after(100, self._animate)
            except:
                self.running = False
