#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Monitor de Seleção de Texto
Detecta seleção de texto globalmente em qualquer aplicação Windows
"""

import time
import threading
import logging
from typing import Callable, Optional
import win32clipboard
import win32con
import win32gui
import win32api
from pynput import keyboard
from pynput.keyboard import Key, KeyCode
from .config_manager import ConfigManager

class TextMonitor:
    def __init__(self, config_manager: ConfigManager, callback: Callable[[str], None]):
        self.config_manager = config_manager
        self.callback = callback
        self.active = False
        self.keyboard_listener = None
        self.last_clipboard_content = ""
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        
        # Configurar atalhos de teclado
        self.shortcuts = self._load_shortcuts()
        
    def _load_shortcuts(self) -> dict:
        """Carrega atalhos de teclado das configurações"""
        shortcuts = {}
        try:
            activate_menu = self.config_manager.get('Shortcuts', 'activate_menu', 'ctrl+shift+a')
            shortcuts['activate_menu'] = self._parse_shortcut(activate_menu)
            
            quick_correct = self.config_manager.get('Shortcuts', 'quick_correct', 'ctrl+shift+c')
            shortcuts['quick_correct'] = self._parse_shortcut(quick_correct)
            
            quick_translate = self.config_manager.get('Shortcuts', 'quick_translate', 'ctrl+shift+t')
            shortcuts['quick_translate'] = self._parse_shortcut(quick_translate)
            
            quick_summarize = self.config_manager.get('Shortcuts', 'quick_summarize', 'ctrl+shift+s')
            shortcuts['quick_summarize'] = self._parse_shortcut(quick_summarize)
            
        except Exception as e:
            logging.error(f"Erro ao carregar atalhos: {e}")
            # Atalhos padrão
            shortcuts = {
                'activate_menu': {Key.ctrl, Key.shift, KeyCode.from_char('a')},
                'quick_correct': {Key.ctrl, Key.shift, KeyCode.from_char('c')},
                'quick_translate': {Key.ctrl, Key.shift, KeyCode.from_char('t')},
                'quick_summarize': {Key.ctrl, Key.shift, KeyCode.from_char('s')}
            }
        
        return shortcuts
    
    def _parse_shortcut(self, shortcut_str: str) -> set:
        """Converte string de atalho em conjunto de teclas"""
        keys = set()
        parts = shortcut_str.lower().split('+')
        
        for part in parts:
            part = part.strip()
            if part == 'ctrl':
                keys.add(Key.ctrl)
            elif part == 'shift':
                keys.add(Key.shift)
            elif part == 'alt':
                keys.add(Key.alt)
            elif part == 'cmd':
                keys.add(Key.cmd)
            elif len(part) == 1:
                keys.add(KeyCode.from_char(part))
            else:
                # Teclas especiais
                special_keys = {
                    'space': Key.space,
                    'enter': Key.enter,
                    'tab': Key.tab,
                    'esc': Key.esc,
                    'f1': Key.f1, 'f2': Key.f2, 'f3': Key.f3, 'f4': Key.f4,
                    'f5': Key.f5, 'f6': Key.f6, 'f7': Key.f7, 'f8': Key.f8,
                    'f9': Key.f9, 'f10': Key.f10, 'f11': Key.f11, 'f12': Key.f12
                }
                if part in special_keys:
                    keys.add(special_keys[part])
        
        return keys
    
    def start(self):
        """Inicia o monitoramento de texto"""
        if self.active:
            return
        
        try:
            self.active = True
            self.stop_event.clear()
            
            # Iniciar listener de teclado
            self.keyboard_listener = keyboard.Listener(
                on_press=self._on_key_press,
                on_release=self._on_key_release
            )
            self.keyboard_listener.start()
            
            # Iniciar thread de monitoramento
            self.monitoring_thread = threading.Thread(
                target=self._monitor_clipboard,
                daemon=True
            )
            self.monitoring_thread.start()
            
            logging.info("Monitoramento de texto iniciado")
            
        except Exception as e:
            logging.error(f"Erro ao iniciar monitoramento: {e}")
            self.active = False
    
    def stop(self):
        """Para o monitoramento de texto"""
        if not self.active:
            return
        
        try:
            self.active = False
            self.stop_event.set()
            
            if self.keyboard_listener:
                self.keyboard_listener.stop()
                self.keyboard_listener = None
            
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=2)
            
            logging.info("Monitoramento de texto parado")
            
        except Exception as e:
            logging.error(f"Erro ao parar monitoramento: {e}")
    
    def is_active(self) -> bool:
        """Verifica se o monitoramento está ativo"""
        return self.active
    
    def _monitor_clipboard(self):
        """Monitora mudanças na área de transferência"""
        while self.active and not self.stop_event.is_set():
            try:
                time.sleep(0.1)  # Verificar a cada 100ms
                
                current_content = self._get_clipboard_text()
                if current_content and current_content != self.last_clipboard_content:
                    # Verificar se é uma seleção de texto válida
                    if self._is_valid_text_selection(current_content):
                        self.last_clipboard_content = current_content
                        # Pequeno delay para evitar múltiplas detecções
                        time.sleep(0.2)
                        
            except Exception as e:
                logging.debug(f"Erro no monitoramento da área de transferência: {e}")
                time.sleep(1)  # Esperar mais tempo em caso de erro
    
    def _get_clipboard_text(self) -> Optional[str]:
        """Obtém texto da área de transferência"""
        try:
            win32clipboard.OpenClipboard()
            if win32clipboard.IsClipboardFormatAvailable(win32con.CF_UNICODETEXT):
                data = win32clipboard.GetClipboardData(win32con.CF_UNICODETEXT)
                win32clipboard.CloseClipboard()
                return data
            else:
                win32clipboard.CloseClipboard()
                return None
        except Exception:
            try:
                win32clipboard.CloseClipboard()
            except:
                pass
            return None
    
    def _is_valid_text_selection(self, text: str) -> bool:
        """Verifica se o texto é uma seleção válida"""
        if not text or not text.strip():
            return False
        
        # Filtrar textos muito curtos ou muito longos
        text_length = len(text.strip())
        if text_length < 3 or text_length > 5000:
            return False
        
        # Filtrar URLs, emails e outros padrões não desejados
        text_lower = text.lower().strip()
        
        # URLs
        if text_lower.startswith(('http://', 'https://', 'www.', 'ftp://')):
            return False
        
        # Emails
        if '@' in text and '.' in text and len(text.split()) == 1:
            return False
        
        # Caminhos de arquivo
        if '\\' in text or '/' in text:
            if any(ext in text_lower for ext in ['.exe', '.dll', '.sys', '.bat', '.cmd']):
                return False
        
        # Códigos ou hashes
        if len(text) > 20 and all(c.isalnum() or c in '-_' for c in text):
            return False
        
        return True
    
    def _on_key_press(self, key):
        """Callback para tecla pressionada"""
        try:
            # Verificar atalhos de teclado
            current_keys = self._get_current_pressed_keys()
            
            for shortcut_name, shortcut_keys in self.shortcuts.items():
                if current_keys == shortcut_keys:
                    self._handle_shortcut(shortcut_name)
                    break
                    
        except Exception as e:
            logging.debug(f"Erro no processamento de tecla: {e}")
    
    def _on_key_release(self, key):
        """Callback para tecla liberada"""
        pass
    
    def _get_current_pressed_keys(self) -> set:
        """Obtém teclas atualmente pressionadas"""
        pressed_keys = set()
        
        # Verificar teclas modificadoras
        if win32api.GetKeyState(win32con.VK_CONTROL) & 0x8000:
            pressed_keys.add(Key.ctrl)
        if win32api.GetKeyState(win32con.VK_SHIFT) & 0x8000:
            pressed_keys.add(Key.shift)
        if win32api.GetKeyState(win32con.VK_MENU) & 0x8000:  # Alt
            pressed_keys.add(Key.alt)
        
        return pressed_keys
    
    def _handle_shortcut(self, shortcut_name: str):
        """Processa atalho de teclado"""
        try:
            # Simular Ctrl+C para copiar texto selecionado
            self._copy_selected_text()
            
            # Aguardar um pouco para o texto ser copiado
            time.sleep(0.1)
            
            # Obter texto copiado
            selected_text = self._get_clipboard_text()
            
            if selected_text and self._is_valid_text_selection(selected_text):
                if shortcut_name == 'activate_menu':
                    self.callback(selected_text)
                elif shortcut_name == 'quick_correct':
                    self._handle_quick_action(selected_text, 'grammar_correction')
                elif shortcut_name == 'quick_translate':
                    self._handle_quick_action(selected_text, 'translation')
                elif shortcut_name == 'quick_summarize':
                    self._handle_quick_action(selected_text, 'summarization')
            
        except Exception as e:
            logging.error(f"Erro ao processar atalho {shortcut_name}: {e}")
    
    def _copy_selected_text(self):
        """Simula Ctrl+C para copiar texto selecionado"""
        try:
            # Simular Ctrl+C
            win32api.keybd_event(win32con.VK_CONTROL, 0, 0, 0)
            win32api.keybd_event(ord('C'), 0, 0, 0)
            win32api.keybd_event(ord('C'), 0, win32con.KEYEVENTF_KEYUP, 0)
            win32api.keybd_event(win32con.VK_CONTROL, 0, win32con.KEYEVENTF_KEYUP, 0)
        except Exception as e:
            logging.error(f"Erro ao copiar texto: {e}")
    
    def _handle_quick_action(self, text: str, action: str):
        """Processa ação rápida de IA"""
        try:
            # Importar e executar ação rápida
            from .quick_actions import QuickActions
            quick_actions = QuickActions(self.config_manager)
            quick_actions.execute_action(text, action, self.callback)
            
        except Exception as e:
            logging.error(f"Erro na ação rápida {action}: {e}")
