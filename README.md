# Assistente de Texto IA

Um assistente de texto inteligente para Windows que utiliza a API do OpenAI ChatGPT para melhorar, traduzir, resumir e processar texto em qualquer aplicação.

## 🚀 Funcionalidades

### Principais Recursos
- **Detecção Global de Texto**: Monitora seleção de texto em qualquer aplicação Windows
- **Menu de Contexto Inteligente**: Acesso rápido às funcionalidades de IA
- **Múltiplas Ações de IA**:
  - Correção gramatical e ortográfica
  - Resumo de textos
  - Tradução para múltiplos idiomas
  - Ajuste de tom (formal/casual)
  - Expansão e elaboração de textos
  - Prompts personalizados

### Interface e Usabilidade
- **Sistema de Bandeja**: Execução discreta em segundo plano
- **Tema Moderno**: Interface com tema Sun Valley (claro/escuro)
- **Atalhos de Teclado**: Acesso rápido às funcionalidades
- **Configuração Intuitiva**: Assistente de configuração inicial

### Segurança
- **Armazenamento Criptografado**: API key protegida com criptografia
- **Configurações Locais**: Dados armazenados apenas no computador do usuário

## 📋 Requisitos

### Sistema
- Windows 10/11
- Python 3.9+ (para desenvolvimento)
- Conexão com internet
- API key da OpenAI

### Dependências Python
```
tkinter (incluído no Python)
sv-ttk==2.6.0
pystray==0.19.5
Pillow==10.1.0
pynput==1.7.6
pywin32==306
openai==1.3.7
requests==2.31.0
cryptography==41.0.8
cx-Freeze==6.15.10
```

## 🛠️ Instalação

### Opção 1: Executável Pronto (Recomendado)
1. Baixe o arquivo `AssistenteTextoIA.exe` da seção Releases
2. Execute o arquivo
3. Siga o assistente de configuração inicial
4. Insira sua API key da OpenAI

### Opção 2: Compilar do Código Fonte
1. Clone o repositório:
```bash
git clone https://github.com/seu-usuario/assistente-texto-ia.git
cd assistente-texto-ia
```

2. Instale as dependências:
```bash
pip install -r requirements.txt
```

3. Execute o build:
```bash
build.bat
```

4. O executável será gerado em `build/exe/AssistenteTextoIA.exe`

## 🔧 Configuração

### Primeira Execução
1. Execute o aplicativo
2. O assistente de configuração será aberto automaticamente
3. Obtenha uma API key da OpenAI em: https://platform.openai.com/api-keys
4. Insira a API key no assistente
5. Configure suas preferências
6. Clique em "Concluir"

### Configurações Avançadas
Acesse as configurações clicando com o botão direito no ícone da bandeja do sistema:

#### OpenAI
- **API Key**: Sua chave de acesso da OpenAI
- **Modelo**: Escolha entre gpt-3.5-turbo, gpt-4, etc.
- **Max Tokens**: Limite de tokens por resposta (100-4000)
- **Temperature**: Criatividade da IA (0.0-2.0)

#### Interface
- **Tema**: Claro ou escuro
- **Notificações**: Ativar/desativar notificações do sistema
- **Auto Iniciar**: Iniciar monitoramento automaticamente

#### Ações
Ative/desative funcionalidades específicas:
- Correção gramatical
- Resumo de texto
- Tradução
- Ajuste de tom
- Expansão de texto
- Prompts personalizados

#### Atalhos de Teclado
Personalize os atalhos (padrão):
- `Ctrl+Shift+A`: Menu de ações
- `Ctrl+Shift+C`: Correção rápida
- `Ctrl+Shift+T`: Tradução rápida
- `Ctrl+Shift+S`: Resumo rápido

## 📖 Como Usar

### Método 1: Menu de Ações
1. Selecione qualquer texto em qualquer aplicação
2. Pressione `Ctrl+Shift+A`
3. Escolha a ação desejada no menu
4. Aguarde o processamento
5. Visualize o resultado e escolha copiar ou substituir

### Método 2: Atalhos Rápidos
1. Selecione texto
2. Use um atalho específico:
   - `Ctrl+Shift+C`: Correção automática
   - `Ctrl+Shift+T`: Tradução para inglês
   - `Ctrl+Shift+S`: Resumo automático

### Método 3: Prompts Personalizados
1. Crie prompts personalizados nas configurações
2. Selecione texto e abra o menu (`Ctrl+Shift+A`)
3. Escolha seu prompt personalizado

## 🎯 Exemplos de Uso

### Correção de Texto
**Entrada**: "Eu gosto muito de programar em python e criar aplicacoes uteis"
**Saída**: "Eu gosto muito de programar em Python e criar aplicações úteis."

### Resumo
**Entrada**: [Texto longo de artigo]
**Saída**: "Resumo conciso dos pontos principais do artigo..."

### Tradução
**Entrada**: "Como você está hoje?"
**Saída**: "How are you today?"

### Ajuste de Tom
**Entrada**: "Oi, preciso que você faça isso rapidinho"
**Saída Formal**: "Prezado(a), solicito gentilmente que realize esta tarefa com brevidade."

## 🔍 Solução de Problemas

### Problemas Comuns

#### "API key inválida"
- Verifique se a API key está correta
- Confirme se há créditos disponíveis na conta OpenAI
- Teste a conexão nas configurações

#### "Texto não é detectado"
- Certifique-se de que o monitoramento está ativo
- Verifique se o texto está realmente selecionado
- Tente usar Ctrl+C antes do atalho

#### "Aplicação não inicia"
- Execute como administrador
- Verifique se não há outra instância rodando
- Consulte o arquivo de log: `ai_text_assistant.log`

### Logs e Depuração
Os logs são salvos em `ai_text_assistant.log` no diretório da aplicação.

## 🔒 Privacidade e Segurança

- **API Key**: Armazenada localmente com criptografia AES
- **Textos Processados**: Enviados apenas para a OpenAI conforme necessário
- **Dados Locais**: Configurações salvas apenas no computador do usuário
- **Sem Telemetria**: Nenhum dado é enviado para terceiros além da OpenAI

## 🤝 Contribuição

Contribuições são bem-vindas! Para contribuir:

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🙏 Agradecimentos

- [OpenAI](https://openai.com/) pela API do ChatGPT
- [Sun Valley TTK Theme](https://github.com/rdbende/Sun-Valley-ttk-theme) pelo tema moderno
- Comunidade Python pelas excelentes bibliotecas

## 📞 Suporte

Para suporte e dúvidas:
- Abra uma [Issue](https://github.com/seu-usuario/assistente-texto-ia/issues)
- Consulte a documentação
- Verifique os logs da aplicação

---

**Desenvolvido com ❤️ para a comunidade brasileira**
