# Assistente de Texto com IA - Dependências
# Interface Gráfica
sv-ttk>=2.6.0

# Sistema de Bandeja
pystray>=0.19.4
Pillow>=10.0.0

# Detecção Global de Eventos
pynput>=1.7.6
pywin32>=306

# Cliente OpenAI
openai>=1.3.0
requests>=2.31.0

# Segurança e Criptografia
cryptography>=41.0.0

# Utilitários (já incluídos no Python)
# configparser - incluído no Python padrão
# threading - incluído no Python padrão

# Build
cx-Freeze>=6.15.0

# Dependência adicional para Python 3.13+
psutil>=5.9.0
