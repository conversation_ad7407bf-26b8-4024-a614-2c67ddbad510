#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Assistente de Configuração Inicial
Guia o usuário na primeira configuração da aplicação
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sv_ttk
import webbrowser
import logging
from .config_manager import ConfigManager

class SetupWizard:
    def __init__(self):
        self.window = None
        self.config_manager = ConfigManager()
        self.current_step = 0
        self.total_steps = 3
        
        # Variáveis de controle
        self.api_key_var = tk.StringVar()
        self.model_var = tk.StringVar(value="gpt-3.5-turbo")
        self.notifications_var = tk.BooleanVar(value=True)
        self.auto_start_var = tk.BooleanVar(value=False)
        
    def run(self) -> bool:
        """Executa o assistente de configuração"""
        try:
            self._create_window()
            self._show_step()
            self.window.mainloop()
            
            # Retornar se a configuração foi concluída
            return self.config_manager.is_configured()
            
        except Exception as e:
            logging.error(f"Erro no assistente de configuração: {e}")
            return False
    
    def _create_window(self):
        """Cria janela do assistente"""
        self.window = tk.Tk()
        self.window.title("Configuração Inicial - Assistente de Texto IA")
        self.window.geometry("600x450")
        self.window.resizable(False, False)
        
        # Aplicar tema
        sv_ttk.set_theme("dark")
        
        # Centralizar janela
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - 300
        y = (self.window.winfo_screenheight() // 2) - 225
        self.window.geometry(f"600x450+{x}+{y}")
        
        # Impedir fechamento direto
        self.window.protocol("WM_DELETE_WINDOW", self._on_close)
        
        # Criar frame principal
        self.main_frame = ttk.Frame(self.window)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Criar frame de navegação
        self.nav_frame = ttk.Frame(self.window)
        self.nav_frame.pack(fill="x", padx=20, pady=(0, 20))
    
    def _show_step(self):
        """Exibe passo atual do assistente"""
        # Limpar frame principal
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # Mostrar passo correspondente
        if self.current_step == 0:
            self._show_welcome_step()
        elif self.current_step == 1:
            self._show_api_key_step()
        elif self.current_step == 2:
            self._show_preferences_step()
        elif self.current_step == 3:
            self._show_completion_step()
        
        # Atualizar botões de navegação
        self._update_navigation()
    
    def _show_welcome_step(self):
        """Exibe passo de boas-vindas"""
        # Título
        ttk.Label(
            self.main_frame, 
            text="Bem-vindo ao Assistente de Texto IA!", 
            font=("", 16, "bold")
        ).pack(pady=(20, 10))
        
        # Descrição
        description = """
Este assistente irá ajudá-lo a configurar o Assistente de Texto IA.

O que você pode fazer com esta aplicação:
• Corrigir gramática e ortografia automaticamente
• Resumir textos longos
• Traduzir para diferentes idiomas
• Ajustar o tom do texto (formal/casual)
• Expandir e elaborar textos
• Usar prompts personalizados

Como funciona:
1. Selecione qualquer texto em qualquer aplicação
2. Use Ctrl+Shift+A para abrir o menu de ações
3. Escolha a ação desejada
4. A IA processará o texto e mostrará o resultado

Vamos começar a configuração!
        """
        
        ttk.Label(
            self.main_frame, 
            text=description, 
            justify="left",
            wraplength=550
        ).pack(pady=20)
    
    def _show_api_key_step(self):
        """Exibe passo de configuração da API key"""
        # Título
        ttk.Label(
            self.main_frame, 
            text="Configuração da API OpenAI", 
            font=("", 16, "bold")
        ).pack(pady=(20, 10))
        
        # Instruções
        instructions = """
Para usar este assistente, você precisa de uma API key da OpenAI.

Se você ainda não tem uma API key:
1. Clique no botão "Obter API Key" abaixo
2. Crie uma conta na OpenAI (se necessário)
3. Gere uma nova API key
4. Copie e cole a API key no campo abaixo

Importante: Sua API key será armazenada de forma segura e criptografada.
        """
        
        ttk.Label(
            self.main_frame, 
            text=instructions, 
            justify="left",
            wraplength=550
        ).pack(pady=(0, 20))
        
        # Botão para obter API key
        ttk.Button(
            self.main_frame, 
            text="Obter API Key da OpenAI", 
            command=self._open_openai_website
        ).pack(pady=(0, 20))
        
        # Campo para API key
        ttk.Label(self.main_frame, text="API Key da OpenAI:").pack(anchor="w")
        
        api_key_frame = ttk.Frame(self.main_frame)
        api_key_frame.pack(fill="x", pady=(5, 10))
        
        self.api_key_entry = ttk.Entry(
            api_key_frame, 
            textvariable=self.api_key_var, 
            show="*", 
            font=("Consolas", 10)
        )
        self.api_key_entry.pack(side="left", fill="x", expand=True)
        
        ttk.Button(
            api_key_frame, 
            text="Mostrar", 
            command=self._toggle_api_key_visibility
        ).pack(side="right", padx=(5, 0))
        
        # Seleção de modelo
        ttk.Label(self.main_frame, text="Modelo da IA:").pack(anchor="w", pady=(20, 5))
        
        model_combo = ttk.Combobox(
            self.main_frame, 
            textvariable=self.model_var,
            values=["gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-4"],
            state="readonly"
        )
        model_combo.pack(fill="x", pady=(0, 10))
        
        # Botão de teste
        ttk.Button(
            self.main_frame, 
            text="Testar Conexão", 
            command=self._test_api_key
        ).pack(pady=10)
    
    def _show_preferences_step(self):
        """Exibe passo de preferências"""
        # Título
        ttk.Label(
            self.main_frame, 
            text="Preferências da Aplicação", 
            font=("", 16, "bold")
        ).pack(pady=(20, 10))
        
        # Opções
        ttk.Checkbutton(
            self.main_frame, 
            text="Mostrar notificações do sistema", 
            variable=self.notifications_var
        ).pack(anchor="w", pady=10)
        
        ttk.Checkbutton(
            self.main_frame, 
            text="Iniciar monitoramento automaticamente", 
            variable=self.auto_start_var
        ).pack(anchor="w", pady=10)
        
        # Informações sobre atalhos
        shortcuts_info = """
Atalhos de teclado padrão:
• Ctrl+Shift+A - Abrir menu de ações
• Ctrl+Shift+C - Correção rápida
• Ctrl+Shift+T - Tradução rápida
• Ctrl+Shift+S - Resumo rápido

Você pode personalizar esses atalhos nas configurações.
        """
        
        info_frame = ttk.LabelFrame(self.main_frame, text="Atalhos de Teclado")
        info_frame.pack(fill="x", pady=20)
        
        ttk.Label(
            info_frame, 
            text=shortcuts_info, 
            justify="left"
        ).pack(padx=10, pady=10)
    
    def _show_completion_step(self):
        """Exibe passo de conclusão"""
        # Título
        ttk.Label(
            self.main_frame, 
            text="Configuração Concluída!", 
            font=("", 16, "bold")
        ).pack(pady=(20, 10))
        
        # Mensagem de sucesso
        success_message = """
Parabéns! O Assistente de Texto IA foi configurado com sucesso.

A aplicação agora está rodando na bandeja do sistema.

Para usar:
1. Selecione qualquer texto em qualquer aplicação
2. Use Ctrl+Shift+A para abrir o menu de ações
3. Escolha a ação desejada

Você pode acessar as configurações clicando com o botão direito 
no ícone da bandeja do sistema.

Aproveite seu novo assistente de texto!
        """
        
        ttk.Label(
            self.main_frame, 
            text=success_message, 
            justify="left",
            wraplength=550
        ).pack(pady=20)
    
    def _update_navigation(self):
        """Atualiza botões de navegação"""
        # Limpar frame de navegação
        for widget in self.nav_frame.winfo_children():
            widget.destroy()
        
        # Indicador de progresso
        progress_text = f"Passo {self.current_step + 1} de {self.total_steps + 1}"
        ttk.Label(self.nav_frame, text=progress_text).pack(side="left")
        
        # Botão Cancelar/Sair
        if self.current_step == self.total_steps:
            ttk.Button(
                self.nav_frame, 
                text="Concluir", 
                command=self._finish_setup
            ).pack(side="right")
        else:
            ttk.Button(
                self.nav_frame, 
                text="Cancelar", 
                command=self._cancel_setup
            ).pack(side="right")
        
        # Botão Próximo
        if self.current_step < self.total_steps:
            ttk.Button(
                self.nav_frame, 
                text="Próximo", 
                command=self._next_step
            ).pack(side="right", padx=(0, 5))
        
        # Botão Anterior
        if self.current_step > 0:
            ttk.Button(
                self.nav_frame, 
                text="Anterior", 
                command=self._previous_step
            ).pack(side="right", padx=(0, 5))
    
    def _next_step(self):
        """Avança para próximo passo"""
        if self._validate_current_step():
            self.current_step += 1
            self._show_step()
    
    def _previous_step(self):
        """Volta para passo anterior"""
        self.current_step -= 1
        self._show_step()
    
    def _validate_current_step(self) -> bool:
        """Valida passo atual"""
        if self.current_step == 1:  # Validar API key
            api_key = self.api_key_var.get().strip()
            if not api_key:
                messagebox.showerror("Erro", "Por favor, insira sua API key da OpenAI.")
                return False
            
            # Salvar configurações temporariamente
            self.config_manager.set_api_key(api_key)
            self.config_manager.set('OpenAI', 'model', self.model_var.get())
        
        return True
    
    def _test_api_key(self):
        """Testa API key inserida"""
        try:
            api_key = self.api_key_var.get().strip()
            if not api_key:
                messagebox.showerror("Erro", "Por favor, insira sua API key.")
                return
            
            # Salvar temporariamente
            old_key = self.config_manager.get_api_key()
            self.config_manager.set_api_key(api_key)
            self.config_manager.set('OpenAI', 'model', self.model_var.get())
            
            # Testar conexão
            from .ai_client import AIClient
            client = AIClient(self.config_manager)
            success = client.test_connection()
            
            if success:
                messagebox.showinfo("Sucesso", "API key válida! Conexão estabelecida.")
            else:
                self.config_manager.set_api_key(old_key)
                messagebox.showerror("Erro", "API key inválida ou erro de conexão.")
                
        except Exception as e:
            messagebox.showerror("Erro", f"Erro no teste: {e}")
    
    def _toggle_api_key_visibility(self):
        """Alterna visibilidade da API key"""
        if self.api_key_entry.cget("show") == "*":
            self.api_key_entry.config(show="")
        else:
            self.api_key_entry.config(show="*")
    
    def _open_openai_website(self):
        """Abre site da OpenAI para obter API key"""
        webbrowser.open("https://platform.openai.com/api-keys")
    
    def _finish_setup(self):
        """Finaliza configuração"""
        try:
            # Salvar preferências finais
            self.config_manager.set('Interface', 'show_notifications', str(self.notifications_var.get()))
            self.config_manager.set('Interface', 'auto_start', str(self.auto_start_var.get()))
            
            # Fechar janela
            self.window.destroy()
            
        except Exception as e:
            logging.error(f"Erro ao finalizar configuração: {e}")
            messagebox.showerror("Erro", f"Erro ao salvar configurações: {e}")
    
    def _cancel_setup(self):
        """Cancela configuração"""
        if messagebox.askyesno("Cancelar", "Deseja cancelar a configuração?\n\nA aplicação será encerrada."):
            self.window.destroy()
    
    def _on_close(self):
        """Callback para fechamento da janela"""
        self._cancel_setup()
