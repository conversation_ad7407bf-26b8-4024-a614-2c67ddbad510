#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Editor de Prompts Personalizados
Interface para criar e editar prompts personalizados
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import sv_ttk
import logging
from .config_manager import ConfigManager

class PromptEditor:
    def __init__(self, parent, config_manager: ConfigManager, prompt_name: str = None):
        self.parent = parent
        self.config_manager = config_manager
        self.prompt_name = prompt_name
        self.window = None
        self.result = False
        
        # Variáveis de controle
        self.name_var = tk.StringVar()
        self.text_var = tk.StringVar()
        
        # Se editando prompt existente, carregar dados
        if prompt_name:
            custom_prompts = config_manager.get_custom_prompts()
            if prompt_name in custom_prompts:
                self.name_var.set(prompt_name)
                self.text_var.set(custom_prompts[prompt_name])
    
    def show(self) -> bool:
        """Exibe o editor de prompts"""
        try:
            self._create_window()
            self.window.mainloop()
            return self.result
        except Exception as e:
            logging.error(f"Erro no editor de prompts: {e}")
            return False
    
    def _create_window(self):
        """Cria janela do editor"""
        self.window = tk.Toplevel(self.parent)
        title = "Editar Prompt" if self.prompt_name else "Novo Prompt"
        self.window.title(f"{title} - Assistente de Texto IA")
        self.window.geometry("500x400")
        self.window.resizable(True, True)
        
        # Aplicar tema
        theme = self.config_manager.get('Interface', 'theme', 'dark')
        sv_ttk.set_theme(theme)
        
        # Centralizar janela
        self._center_window()
        
        # Configurar como modal
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Criar interface
        self._create_interface()
        
        # Configurar fechamento
        self.window.protocol("WM_DELETE_WINDOW", self._cancel)
        self.window.bind("<Escape>", lambda e: self._cancel())
        
        # Focar no campo nome
        self.name_entry.focus_set()
    
    def _center_window(self):
        """Centraliza janela em relação ao pai"""
        self.window.update_idletasks()
        
        # Obter posição e tamanho da janela pai
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Calcular posição central
        width = 500
        height = 400
        x = parent_x + (parent_width // 2) - (width // 2)
        y = parent_y + (parent_height // 2) - (height // 2)
        
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_interface(self):
        """Cria interface do editor"""
        # Frame principal
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Título
        title = "Editar Prompt Personalizado" if self.prompt_name else "Novo Prompt Personalizado"
        ttk.Label(main_frame, text=title, font=("", 12, "bold")).pack(pady=(0, 20))
        
        # Nome do prompt
        ttk.Label(main_frame, text="Nome do Prompt:").pack(anchor="w")
        self.name_entry = ttk.Entry(main_frame, textvariable=self.name_var, font=("", 10))
        self.name_entry.pack(fill="x", pady=(5, 15))
        
        # Texto do prompt
        ttk.Label(main_frame, text="Texto do Prompt:").pack(anchor="w")
        
        # Instruções
        instructions = ttk.Label(
            main_frame, 
            text="Escreva o prompt que será enviado para a IA. O texto selecionado será adicionado automaticamente.",
            font=("", 9),
            foreground="gray"
        )
        instructions.pack(anchor="w", pady=(5, 10))
        
        # Área de texto
        self.text_widget = scrolledtext.ScrolledText(
            main_frame, 
            wrap=tk.WORD, 
            height=12,
            font=("", 10)
        )
        self.text_widget.pack(fill="both", expand=True, pady=(0, 15))
        
        # Inserir texto inicial se disponível
        if self.text_var.get():
            self.text_widget.insert("1.0", self.text_var.get())
        
        # Exemplos
        examples_frame = ttk.LabelFrame(main_frame, text="Exemplos de Prompts")
        examples_frame.pack(fill="x", pady=(0, 15))
        
        examples_text = """• Melhore a escrita do seguinte texto:
• Transforme o seguinte texto em uma apresentação:
• Crie um resumo executivo do seguinte conteúdo:
• Explique o seguinte conceito para uma criança:"""
        
        ttk.Label(examples_frame, text=examples_text, justify="left", font=("", 9)).pack(padx=10, pady=10)
        
        # Botões
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill="x")
        
        ttk.Button(buttons_frame, text="Cancelar", command=self._cancel).pack(side="right")
        ttk.Button(buttons_frame, text="Salvar", command=self._save).pack(side="right", padx=(0, 10))
        
        # Botão de teste (se editando)
        if self.prompt_name:
            ttk.Button(buttons_frame, text="Testar", command=self._test_prompt).pack(side="left")
    
    def _save(self):
        """Salva o prompt"""
        try:
            # Validar dados
            name = self.name_var.get().strip()
            text = self.text_widget.get("1.0", tk.END).strip()
            
            if not name:
                messagebox.showerror("Erro", "Por favor, insira um nome para o prompt.")
                self.name_entry.focus_set()
                return
            
            if not text:
                messagebox.showerror("Erro", "Por favor, insira o texto do prompt.")
                self.text_widget.focus_set()
                return
            
            # Verificar se nome já existe (apenas para novos prompts)
            if not self.prompt_name:
                existing_prompts = self.config_manager.get_custom_prompts()
                if name in existing_prompts:
                    if not messagebox.askyesno(
                        "Prompt Existente", 
                        f"Já existe um prompt com o nome '{name}'.\n\nDeseja substituí-lo?"
                    ):
                        return
            
            # Remover prompt antigo se editando
            if self.prompt_name and self.prompt_name != name:
                self.config_manager.remove_custom_prompt(self.prompt_name)
            
            # Salvar prompt
            self.config_manager.add_custom_prompt(name, text)
            
            # Sucesso
            self.result = True
            self.window.destroy()
            
        except Exception as e:
            logging.error(f"Erro ao salvar prompt: {e}")
            messagebox.showerror("Erro", f"Erro ao salvar prompt: {e}")
    
    def _test_prompt(self):
        """Testa o prompt com texto de exemplo"""
        try:
            text = self.text_widget.get("1.0", tk.END).strip()
            if not text:
                messagebox.showerror("Erro", "Por favor, insira o texto do prompt primeiro.")
                return
            
            # Texto de exemplo
            example_text = "Este é um texto de exemplo para testar o prompt personalizado."
            
            # Criar janela de teste
            test_window = tk.Toplevel(self.window)
            test_window.title("Teste do Prompt")
            test_window.geometry("600x400")
            test_window.transient(self.window)
            test_window.grab_set()
            
            # Centralizar
            test_window.update_idletasks()
            x = self.window.winfo_x() + 50
            y = self.window.winfo_y() + 50
            test_window.geometry(f"600x400+{x}+{y}")
            
            # Interface de teste
            test_frame = ttk.Frame(test_window)
            test_frame.pack(fill="both", expand=True, padx=20, pady=20)
            
            ttk.Label(test_frame, text="Visualização do Prompt", font=("", 12, "bold")).pack(pady=(0, 10))
            
            # Prompt completo
            full_prompt = f"{text}\n\nTexto:\n{example_text}"
            
            test_text = scrolledtext.ScrolledText(test_frame, wrap=tk.WORD, height=15)
            test_text.pack(fill="both", expand=True, pady=(0, 10))
            test_text.insert("1.0", full_prompt)
            test_text.config(state="disabled")
            
            # Botão fechar
            ttk.Button(test_frame, text="Fechar", command=test_window.destroy).pack()
            
        except Exception as e:
            logging.error(f"Erro no teste do prompt: {e}")
            messagebox.showerror("Erro", f"Erro no teste: {e}")
    
    def _cancel(self):
        """Cancela edição"""
        self.result = False
        self.window.destroy()
