#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ações Rápidas
Executa ações de IA rapidamente através de atalhos de teclado
"""

import threading
import logging
from typing import Callable
from .config_manager import ConfigManager
from .ai_client import AIClient

class QuickActions:
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.ai_client = AIClient(config_manager)
    
    def execute_action(self, text: str, action: str, callback: Callable):
        """Executa ação rápida"""
        try:
            # Executar em thread separada
            thread = threading.Thread(
                target=self._process_quick_action,
                args=(text, action, callback),
                daemon=True
            )
            thread.start()
            
        except Exception as e:
            logging.error(f"Erro ao executar ação rápida {action}: {e}")
    
    def _process_quick_action(self, text: str, action: str, callback: Callable):
        """Processa ação rápida em thread separada"""
        try:
            # Mapear ações para IDs da IA
            action_mapping = {
                'grammar_correction': 'grammar_correction',
                'translation': 'translation_en',
                'summarization': 'text_summarization'
            }
            
            ai_action = action_mapping.get(action, action)
            
            # Processar com IA
            response = self.ai_client.process_text(text, ai_action)
            
            if response:
                # Chamar callback com resultado
                callback(text, response, ai_action)
            else:
                self._show_error("Não foi possível processar o texto.")
                
        except Exception as e:
            logging.error(f"Erro no processamento da ação rápida {action}: {e}")
            self._show_error(f"Erro: {str(e)}")
    
    def _show_error(self, message: str):
        """Exibe mensagem de erro"""
        try:
            import tkinter as tk
            from tkinter import messagebox
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Erro", message)
            root.destroy()
        except Exception as e:
            logging.error(f"Erro ao exibir mensagem: {e}")
